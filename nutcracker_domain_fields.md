# Nutcracker DIV3 Domain Field Mapping

## 🔧 CONFIGURATION_MANAGEMENT

### Analysis Policies
```yaml
analysis_policies:
  max_analysis_time: integer (seconds)
  max_file_size: integer (bytes)
  max_recursion_depth: integer
  max_extracted_files: integer
  timeout_behavior: enum [abort, partial_results, extend]
  priority_rules: array[priority_rule]
  file_type_policies: map[file_type, policy]

priority_rule:
  condition: string (expression)
  priority_level: enum [low, normal, high, critical]
  resource_allocation: float (0.0-1.0)
```

### Plugin Settings
```yaml
plugin_settings:
  yara:
    enabled: boolean
    rule_directories: array[string]
    max_matches: integer
    timeout: integer
    memory_limit: integer
  
  mwcp:
    enabled: boolean
    parsers: array[string]
    timeout: integer
    max_output_size: integer
  
  antivirus:
    enabled: boolean
    engines: array[av_engine]
    update_frequency: integer
    quarantine_threshold: float
  
  behavioral:
    enabled: boolean
    vm_timeout: integer
    network_simulation: boolean
    api_monitoring: boolean
```

### Resource Limits
```yaml
resource_limits:
  max_concurrent_analyses: integer
  max_memory_per_analysis: integer (MB)
  max_cpu_cores: integer
  max_disk_space: integer (GB)
  vm_pool_size: integer
  network_bandwidth_limit: integer (Mbps)
  
  queue_limits:
    max_pending_tasks: integer
    max_queue_size: integer (MB)
    retention_period: integer (hours)
```

### Detection Thresholds
```yaml
detection_thresholds:
  malware_confidence: float (0.0-1.0)
  suspicious_threshold: float (0.0-1.0)
  false_positive_threshold: float (0.0-1.0)
  
  scoring_weights:
    static_analysis: float
    dynamic_analysis: float
    reputation: float
    behavioral: float
    ml_prediction: float
```

## 🟨 THREAT_INTELLIGENCE

### Signature Database
```yaml
signature_database:
  yara_rules:
    rule_id: string (UUID)
    rule_name: string
    rule_content: string (YARA syntax)
    author: string
    creation_date: datetime
    last_modified: datetime
    severity: enum [info, low, medium, high, critical]
    tags: array[string]
    enabled: boolean
    false_positive_rate: float
  
  av_signatures:
    signature_id: string
    engine_name: string
    signature_name: string
    detection_type: enum [virus, trojan, worm, adware, etc.]
    last_updated: datetime
    confidence: float
```

### IOC Repository
```yaml
ioc_repository:
  file_hashes:
    hash_value: string
    hash_type: enum [md5, sha1, sha256, sha512, ssdeep]
    classification: enum [malicious, suspicious, clean, unknown]
    first_seen: datetime
    last_seen: datetime
    source: string
    confidence: float
    family: string
    
  network_indicators:
    indicator_type: enum [ip, domain, url, email]
    indicator_value: string
    classification: enum [malicious, suspicious, clean]
    first_seen: datetime
    last_seen: datetime
    source: string
    confidence: float
    
  behavioral_indicators:
    behavior_id: string
    behavior_description: string
    severity: enum [low, medium, high, critical]
    mitre_technique: string
    detection_logic: string
```

### Reputation Services
```yaml
reputation_services:
  service_config:
    service_name: string
    api_endpoint: string
    api_key: string
    rate_limit: integer (requests/minute)
    timeout: integer (seconds)
    enabled: boolean
    
  reputation_cache:
    hash_value: string
    reputation_score: float (-1.0 to 1.0)
    vendor_name: string
    last_checked: datetime
    cache_expiry: datetime
    raw_response: json
```

## 🟦 WORKFLOW_ORCHESTRATION

### Task Scheduler
```yaml
task_scheduler:
  analysis_task:
    task_id: string (UUID)
    submission_id: string
    priority: enum [low, normal, high, critical]
    created_at: datetime
    scheduled_at: datetime
    started_at: datetime
    completed_at: datetime
    status: enum [pending, running, completed, failed, cancelled]
    retry_count: integer
    max_retries: integer
    
  task_dependencies:
    task_id: string
    depends_on: array[string] (task_ids)
    dependency_type: enum [sequential, parallel, conditional]
```

### Analysis Pipeline
```yaml
analysis_pipeline:
  pipeline_stage:
    stage_id: string
    stage_name: string
    stage_type: enum [preprocessing, static, dynamic, postprocessing]
    execution_order: integer
    required: boolean
    timeout: integer
    retry_policy: retry_config
    
  pipeline_execution:
    execution_id: string
    task_id: string
    current_stage: string
    completed_stages: array[string]
    failed_stages: array[string]
    stage_results: map[stage_id, stage_result]
```

## 🟧 FILE_HANDLING

### File Validator
```yaml
file_validator:
  file_metadata:
    file_id: string (UUID)
    original_filename: string
    file_size: integer (bytes)
    mime_type: string
    file_extension: string
    upload_timestamp: datetime
    source_ip: string
    submission_method: enum [web, api, email, bulk]

  validation_results:
    is_valid: boolean
    file_format: string
    corruption_detected: boolean
    encryption_detected: boolean
    password_protected: boolean
    validation_errors: array[string]
    magic_signature: string

### Archive Extractor
```yaml
archive_extractor:
  extraction_metadata:
    extraction_id: string (UUID)
    parent_file_id: string
    archive_type: enum [zip, rar, 7z, tar, gzip, bzip2, cab, iso]
    password_required: boolean
    passwords_attempted: array[string]
    extraction_depth: integer
    total_extracted_files: integer
    extraction_timestamp: datetime

  extracted_file:
    extracted_file_id: string (UUID)
    parent_file_id: string
    relative_path: string
    file_size: integer
    compression_ratio: float
    crc32: string
    extraction_method: string
    is_encrypted: boolean

### Packer Detector/Unpacker
```yaml
packer_detector:
  detection_results:
    file_id: string
    is_packed: boolean
    packer_type: enum [upx, aspack, pecompact, themida, vmprotect, custom]
    packer_version: string
    confidence: float (0.0-1.0)
    detection_method: enum [signature, entropy, heuristic, ml]

  unpacking_results:
    unpacking_successful: boolean
    unpacked_file_id: string
    original_entry_point: hex_address
    unpacked_entry_point: hex_address
    unpacking_method: string
    unpacking_errors: array[string]

### Hash Calculator
```yaml
hash_calculator:
  file_hashes:
    file_id: string
    md5: string (32 chars)
    sha1: string (40 chars)
    sha256: string (64 chars)
    sha512: string (128 chars)
    ssdeep: string
    imphash: string (PE files)
    authentihash: string (signed files)
    calculation_timestamp: datetime

  hash_relationships:
    similar_files: array[similarity_match]
    hash_clusters: array[string] (file_ids)

  similarity_match:
    file_id: string
    similarity_score: float (0.0-1.0)
    hash_type: enum [ssdeep, tlsh, sdhash]
    match_reason: string

### Metadata Extractor
```yaml
metadata_extractor:
  file_properties:
    file_id: string
    entropy: float (0.0-8.0)
    file_type_confidence: float
    creation_time: datetime
    modification_time: datetime
    access_time: datetime

  pe_metadata: # For PE files
    compile_timestamp: datetime
    linker_version: string
    subsystem: string
    machine_type: string
    sections: array[pe_section]
    imports: array[import_function]
    exports: array[export_function]
    resources: array[resource_entry]
    digital_signature: signature_info

  pe_section:
    section_name: string
    virtual_address: hex_address
    virtual_size: integer
    raw_size: integer
    characteristics: array[string]
    entropy: float

  document_metadata: # For Office/PDF files
    author: string
    title: string
    subject: string
    creation_date: datetime
    modification_date: datetime
    application: string
    embedded_objects: array[embedded_object]
    macros_detected: boolean

  embedded_object:
    object_type: enum [ole, embedded_file, macro, javascript, vba]
    object_size: integer
    object_hash: string
    suspicious_indicators: array[string]
```

## 🟩 SANDBOX_ENVIRONMENT

### VM Manager
```yaml
vm_manager:
  virtual_machine:
    vm_id: string (UUID)
    vm_name: string
    os_type: enum [windows_7, windows_10, windows_11, ubuntu, centos]
    os_version: string
    architecture: enum [x86, x64, arm64]
    vm_state: enum [stopped, starting, running, stopping, suspended, error]
    created_at: datetime
    last_used: datetime
    usage_count: integer

  vm_configuration:
    cpu_cores: integer
    memory_mb: integer
    disk_size_gb: integer
    network_enabled: boolean
    internet_access: boolean
    snapshot_enabled: boolean
    monitoring_tools: array[string]

  vm_pool:
    pool_id: string
    pool_name: string
    os_template: string
    min_instances: integer
    max_instances: integer
    current_instances: integer
    available_instances: integer

### Isolation Controller
```yaml
isolation_controller:
  isolation_session:
    session_id: string (UUID)
    vm_id: string
    analysis_task_id: string
    start_time: datetime
    end_time: datetime
    isolation_level: enum [full, network_only, filesystem_only]

  network_isolation:
    virtual_network_id: string
    subnet_range: string (CIDR)
    gateway_ip: string
    dns_servers: array[string]
    firewall_rules: array[firewall_rule]
    traffic_capture_enabled: boolean

  filesystem_isolation:
    mount_points: array[mount_point]
    read_only_paths: array[string]
    monitored_paths: array[string]
    file_access_log: array[file_access_event]

### System Monitor
```yaml
system_monitor:
  process_monitoring:
    process_id: integer
    parent_process_id: integer
    process_name: string
    command_line: string
    start_time: datetime
    end_time: datetime
    user_context: string
    process_tree: array[process_info]

  api_monitoring:
    api_call_id: string (UUID)
    process_id: integer
    api_name: string
    parameters: array[parameter]
    return_value: string
    timestamp: datetime
    call_stack: array[string]

  registry_monitoring:
    registry_event_id: string (UUID)
    event_type: enum [create, modify, delete, query]
    registry_path: string
    value_name: string
    old_value: string
    new_value: string
    process_id: integer
    timestamp: datetime

  file_monitoring:
    file_event_id: string (UUID)
    event_type: enum [create, modify, delete, rename, access]
    file_path: string
    process_id: integer
    timestamp: datetime
    file_size: integer
    file_hash: string

### Network Capture
```yaml
network_capture:
  network_session:
    session_id: string (UUID)
    vm_id: string
    start_time: datetime
    end_time: datetime
    total_packets: integer
    total_bytes: integer

  network_connection:
    connection_id: string (UUID)
    protocol: enum [tcp, udp, icmp, http, https, dns, smtp]
    source_ip: string
    source_port: integer
    destination_ip: string
    destination_port: integer
    connection_state: enum [established, closed, listening]
    bytes_sent: integer
    bytes_received: integer

  dns_query:
    query_id: string (UUID)
    query_type: enum [A, AAAA, MX, TXT, CNAME]
    domain_name: string
    response_ip: array[string]
    response_time: integer (ms)
    timestamp: datetime

  http_request:
    request_id: string (UUID)
    method: enum [GET, POST, PUT, DELETE, HEAD]
    url: string
    headers: map[string, string]
    body: string
    response_code: integer
    response_headers: map[string, string]
    response_body: string
    timestamp: datetime

## 🟥 DETECTION_ENGINES

### Static Analysis
```yaml
static_analysis:
  pe_analysis:
    file_id: string
    is_pe_file: boolean
    pe_type: enum [exe, dll, sys, ocx]
    architecture: enum [x86, x64, arm, arm64]
    subsystem: enum [console, gui, driver, native]
    entry_point: hex_address
    image_base: hex_address
    checksum_valid: boolean

  string_analysis:
    extracted_strings: array[extracted_string]
    suspicious_strings: array[suspicious_string]
    encryption_indicators: array[string]
    network_indicators: array[string]

  extracted_string:
    string_value: string
    string_type: enum [ascii, unicode, base64, hex]
    offset: integer
    length: integer
    entropy: float

  code_analysis:
    disassembly_available: boolean
    control_flow_graph: graph_data
    function_count: integer
    suspicious_functions: array[function_info]
    obfuscation_detected: boolean
    anti_analysis_techniques: array[string]

### Dynamic Analysis
```yaml
dynamic_analysis:
  execution_summary:
    file_id: string
    execution_successful: boolean
    execution_time: integer (seconds)
    exit_code: integer
    crashed: boolean
    crash_reason: string

  behavioral_indicators:
    file_operations: array[file_operation]
    registry_operations: array[registry_operation]
    network_operations: array[network_operation]
    process_operations: array[process_operation]

  file_operation:
    operation_type: enum [create, delete, modify, copy, move]
    file_path: string
    timestamp: datetime
    success: boolean

  persistence_mechanisms:
    autorun_entries: array[autorun_entry]
    scheduled_tasks: array[scheduled_task]
    service_installations: array[service_info]

  network_behavior:
    connections_attempted: integer
    connections_successful: integer
    domains_contacted: array[string]
    ip_addresses_contacted: array[string]
    protocols_used: array[string]

### YARA Engine
```yaml
yara_engine:
  yara_match:
    rule_id: string
    rule_name: string
    rule_namespace: string
    match_offset: integer
    match_length: integer
    match_data: string
    confidence: float

  rule_execution:
    file_id: string
    rules_loaded: integer
    rules_executed: integer
    execution_time: integer (ms)
    memory_usage: integer (MB)
    matches_found: integer

  rule_metadata:
    rule_author: string
    rule_description: string
    rule_reference: string
    rule_date: datetime
    rule_version: string
    malware_family: string
    severity: enum [info, low, medium, high, critical]

### Behavioral Analysis
```yaml
behavioral_analysis:
  behavior_pattern:
    pattern_id: string
    pattern_name: string
    pattern_description: string
    mitre_technique: string
    confidence: float
    evidence: array[evidence_item]

  evidence_item:
    evidence_type: enum [api_call, file_access, registry_access, network_activity]
    evidence_data: string
    timestamp: datetime
    process_id: integer

  malware_capabilities:
    data_theft: boolean
    credential_harvesting: boolean
    keylogging: boolean
    screen_capture: boolean
    remote_access: boolean
    cryptocurrency_mining: boolean
    ransomware_behavior: boolean

  communication_patterns:
    c2_communication: boolean
    c2_servers: array[string]
    communication_protocol: array[string]
    encryption_used: boolean
    data_exfiltration: boolean

### Machine Learning
```yaml
machine_learning:
  ml_prediction:
    model_id: string
    model_version: string
    prediction_confidence: float (0.0-1.0)
    prediction_class: enum [benign, suspicious, malicious]
    feature_vector: array[float]
    prediction_timestamp: datetime

  feature_extraction:
    static_features: array[static_feature]
    dynamic_features: array[dynamic_feature]
    behavioral_features: array[behavioral_feature]

  static_feature:
    feature_name: string
    feature_value: float
    feature_importance: float

  model_metadata:
    training_date: datetime
    training_samples: integer
    accuracy: float
    false_positive_rate: float
    false_negative_rate: float
```

## 🟪 ANALYSIS_COORDINATION

### Analysis Orchestrator
```yaml
analysis_orchestrator:
  analysis_session:
    session_id: string (UUID)
    file_id: string
    analysis_type: enum [quick, standard, deep, custom]
    priority: enum [low, normal, high, critical]
    requested_engines: array[string]
    start_time: datetime
    end_time: datetime
    status: enum [queued, running, completed, failed, cancelled]

  engine_coordination:
    engine_name: string
    execution_order: integer
    depends_on: array[string] (engine names)
    timeout: integer (seconds)
    retry_count: integer
    status: enum [pending, running, completed, failed, skipped]
    start_time: datetime
    end_time: datetime

### Result Correlator
```yaml
result_correlator:
  correlation_analysis:
    session_id: string
    correlation_confidence: float (0.0-1.0)
    conflicting_results: array[conflict_info]
    consensus_reached: boolean

  conflict_info:
    engine_a: string
    engine_b: string
    conflict_type: enum [classification, severity, family]
    engine_a_result: string
    engine_b_result: string
    resolution_method: enum [confidence_based, majority_vote, manual]

  cross_reference:
    indicator_type: enum [hash, domain, ip, behavior]
    indicator_value: string
    supporting_engines: array[string]
    confidence_score: float

### Risk Scorer
```yaml
risk_scorer:
  risk_assessment:
    file_id: string
    overall_risk_score: float (0.0-10.0)
    risk_category: enum [benign, low, medium, high, critical]
    confidence: float (0.0-1.0)

  scoring_components:
    static_score: float
    dynamic_score: float
    reputation_score: float
    behavioral_score: float
    ml_score: float

  risk_factors:
    factor_name: string
    factor_weight: float
    factor_score: float
    factor_evidence: array[string]

  threat_classification:
    malware_family: string
    malware_type: enum [virus, trojan, worm, ransomware, spyware, adware]
    attack_vector: enum [email, web, usb, network, unknown]
    target_platform: enum [windows, linux, macos, android, ios, multi]

## 🟣 REPORTING_GENERATION

### Data Aggregator
```yaml
data_aggregator:
  analysis_summary:
    file_id: string
    analysis_session_id: string
    submission_timestamp: datetime
    analysis_completion_timestamp: datetime
    total_analysis_time: integer (seconds)
    engines_executed: array[string]
    engines_successful: array[string]
    engines_failed: array[string]

  aggregated_results:
    overall_verdict: enum [clean, suspicious, malicious, unknown]
    confidence_level: float (0.0-1.0)
    risk_score: float (0.0-10.0)
    threat_level: enum [none, low, medium, high, critical]

  detection_summary:
    total_detections: integer
    unique_families: array[string]
    detection_engines: array[detection_result]

  detection_result:
    engine_name: string
    detection_name: string
    detection_type: string
    confidence: float
    severity: enum [info, low, medium, high, critical]

### Multi-Format Generator
```yaml
multi_format_generator:
  report_formats:
    json_report:
      format_version: string
      schema_version: string
      compression: enum [none, gzip, zip]
      pretty_print: boolean

    xml_report:
      schema_location: string
      namespace: string
      validation_enabled: boolean

    pdf_report:
      template_id: string
      include_screenshots: boolean
      include_graphs: boolean
      page_orientation: enum [portrait, landscape]

    html_report:
      template_id: string
      interactive_elements: boolean
      embedded_resources: boolean

  export_options:
    include_raw_data: boolean
    include_metadata: boolean
    include_iocs: boolean
    include_recommendations: boolean
    sanitize_sensitive_data: boolean

### Executive Summary
```yaml
executive_summary:
  summary_content:
    executive_overview: string
    key_findings: array[string]
    risk_assessment: string
    recommendations: array[recommendation]

  recommendation:
    priority: enum [low, medium, high, critical]
    action_type: enum [block, quarantine, monitor, investigate]
    description: string
    rationale: string

  business_impact:
    impact_level: enum [none, low, medium, high, critical]
    affected_systems: array[string]
    potential_damage: string
    mitigation_urgency: enum [immediate, within_24h, within_week, routine]

### Technical Details
```yaml
technical_details:
  file_analysis:
    file_properties: file_metadata
    hash_analysis: hash_results
    static_analysis: static_analysis_results
    dynamic_analysis: dynamic_analysis_results

  detection_details:
    yara_matches: array[yara_match]
    av_detections: array[av_detection]
    behavioral_indicators: array[behavior_indicator]
    network_indicators: array[network_indicator]

  forensic_artifacts:
    created_files: array[file_artifact]
    modified_registry: array[registry_artifact]
    network_connections: array[network_artifact]
    process_activity: array[process_artifact]

  file_artifact:
    file_path: string
    file_hash: string
    file_size: integer
    creation_time: datetime
    purpose: string

### IOC Extractor
```yaml
ioc_extractor:
  extracted_iocs:
    file_hashes: array[hash_ioc]
    network_iocs: array[network_ioc]
    registry_iocs: array[registry_ioc]
    file_system_iocs: array[filesystem_ioc]

  hash_ioc:
    hash_type: enum [md5, sha1, sha256, sha512, ssdeep]
    hash_value: string
    confidence: float
    context: string

  network_ioc:
    ioc_type: enum [ip, domain, url, email]
    ioc_value: string
    port: integer
    protocol: string
    confidence: float
    first_seen: datetime

  ioc_formatting:
    stix_format: boolean
    misp_format: boolean
    csv_format: boolean
    yara_rules: boolean
    snort_rules: boolean

### Timeline Builder
```yaml
timeline_builder:
  timeline_event:
    event_id: string (UUID)
    timestamp: datetime
    event_type: enum [file_access, registry_change, network_activity, process_start]
    event_description: string
    process_id: integer
    process_name: string
    severity: enum [info, warning, suspicious, malicious]

  timeline_analysis:
    total_events: integer
    time_span: integer (seconds)
    peak_activity_period: time_range
    suspicious_patterns: array[pattern_description]

  time_range:
    start_time: datetime
    end_time: datetime
    event_count: integer

  pattern_description:
    pattern_name: string
    pattern_description: string
    confidence: float
    supporting_events: array[string] (event_ids)
```

## ⬜ PERFORMANCE_MONITORING

### Performance Metrics
```yaml
performance_metrics:
  system_metrics:
    cpu_usage_percent: float
    memory_usage_mb: integer
    disk_usage_gb: float
    network_throughput_mbps: float
    active_analyses: integer
    queued_analyses: integer

  analysis_metrics:
    average_analysis_time: integer (seconds)
    analyses_per_hour: integer
    success_rate: float (0.0-1.0)
    error_rate: float (0.0-1.0)
    timeout_rate: float (0.0-1.0)

  engine_performance:
    engine_name: string
    average_execution_time: integer (ms)
    success_rate: float
    error_count: integer
    memory_usage: integer (MB)
    cpu_usage: float

### Resource Monitor
```yaml
resource_monitor:
  resource_usage:
    timestamp: datetime
    cpu_cores_used: integer
    memory_allocated_mb: integer
    disk_space_used_gb: float
    network_bandwidth_used_mbps: float
    vm_instances_active: integer

  resource_limits:
    max_cpu_usage: float (0.0-1.0)
    max_memory_usage: integer (MB)
    max_disk_usage: integer (GB)
    max_concurrent_analyses: integer

  resource_alerts:
    alert_id: string (UUID)
    alert_type: enum [cpu_high, memory_high, disk_full, queue_full]
    threshold_exceeded: float
    current_value: float
    timestamp: datetime
    severity: enum [warning, critical]

### Analysis Timing
```yaml
analysis_timing:
  timing_breakdown:
    file_id: string
    total_time: integer (seconds)
    preprocessing_time: integer
    static_analysis_time: integer
    dynamic_analysis_time: integer
    postprocessing_time: integer

  engine_timing:
    engine_name: string
    start_time: datetime
    end_time: datetime
    execution_time: integer (ms)
    wait_time: integer (ms)

  bottleneck_analysis:
    slowest_engine: string
    slowest_operation: string
    optimization_suggestions: array[string]

### Performance Alerts
```yaml
performance_alerts:
  alert_definition:
    alert_id: string (UUID)
    alert_name: string
    metric_name: string
    threshold_value: float
    comparison_operator: enum [greater_than, less_than, equals]
    alert_severity: enum [info, warning, critical]

  alert_instance:
    alert_instance_id: string (UUID)
    alert_definition_id: string
    triggered_at: datetime
    resolved_at: datetime
    current_value: float
    threshold_value: float
    status: enum [active, acknowledged, resolved]

  alert_actions:
    action_type: enum [email, webhook, log, throttle, shutdown]
    action_parameters: map[string, string]
    execution_status: enum [pending, executed, failed]
```
```
```
