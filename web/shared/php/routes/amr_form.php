<?php
namespace EMS;
use <PERSON>MS\Exception\HTTPForbidden;
use EMS\Exception\HTTPNotFound;
use EMS\Exception\HTTPBadRequest;
use EMS\Util\Util;
$app = Ems::get_app();

$app->get("/amr/form/single", function () use ($app) {
    $req = $app->request;
    $res = $app->response;
    $log = $app->logger;

    $log->debug("HTTP/GET /amr/form/single");
    Util::check_if_user_logged_in($req, $log);
    if ($req->permissions[PRIVILEGE_VIEW_FORM_1A] == false) {
        // Handle requests from users without the proper permissions
        $log->error("The user does not have the correct permissions.");
        throw new HTTPForbidden("You do not have the proper user permissions", "Permissions Error");
    }

    $userid = $req->user_id;
    $tracking_num = Util::get_required_param($req, 'tracking_num', $log);
    $log->debug("[AMR][FORM][SINGLE][".$userid."] Searching for AMR form " . $tracking_num . "...");

    $form = $app->db->get_single_amr($tracking_num);
    if (!$form) {
        // Handle no form found
        $log->error("The AMR form " . $tracking_num . " was NOT found.");
        throw new HTTPNotFound("The server could not find the submission you were looking for.", "Submission Not Found");
    }
    $log->debug("[AMR][FORM][SINGLE][".$userid."] AMR form " . $tracking_num . " was found");

    // Checking if user can access
    if ($app->domain === EMS_DOMAIN_INTERNAL) { // Internal
        $user = $app->db->get_user_by_user_id($userid);
        $user_role = $user["user_role"];
        // Check to see if they should be allowed to see this particular form
        if (($user_role == EMS_ADMIN) || // EMS Admin can see no matter what
            ($user_role == EMS_DCISE_MAC && $form["directorate"] == DIRECTORATE_DCISE) || // DCISE MAC can see only for DCISE submissions
            ($user_role == EMS_AG_MAC && $form["directorate"] == DIRECTORATE_AG)) { // AG can see only for AG submissions
            // User can view this exam
        }
        else {
            $log->error("[AMR][FORM][SINGLE][".$userid."] User has role " . $user_role . ". The form is for directorate " . $form["directorate"] . ". The user is not permitted to view this AMR submission.");
            throw new HTTPForbidden("You are not permitted to view this AMR submission.");
        }
    }
    else { // External
        $owner = $form["owner"];
        $members = in_array($owner, $app->db->get_service_ids()) ? $app->db->get_service_members_user_ids($owner) : array();

        // Handle requests for a submission that the user is not supposed to have access to
        if ($owner != $userid && !in_array($userid, $members) && !is_dc3_user_from_same_directorate($app->db, $tracking_num, $userid)) {
            $log->error("The user is attempting to view a form that is not theirs.");
            throw new HTTPForbidden("You cannot view the submission because you are not the owner.", "Ownership Error");
        }
    }

    // Add service details if the AMR was submitted by a service
    if ($form['submitted_source'] == 'SERVICE') {
        $service = $app->db->get_service_by_tracking_number($tracking_num);
        $service["service_created_timestamp"] = Util::createDateTime($service["service_created_timestamp"]);
        $service['service_members'] = $app->db->get_service_members($service['service_id']);
        $form = array_merge($form, $service);
    }

    if (array_key_exists("submit_date", $form)) {
        $form["submit_date"] = Util::createDateTime($form["submit_date"]);
    }
    $response_body['amr'] = $form;
    $response_body['amr_mappings'] = $app->settings['amr_mappings'];
    $response_body['amr_resubmission_interval_days'] = $app->settings['amr_resubmission_interval_days'];

    $res->setBody(json_encode($response_body));
    $log->debug("[AMR][FORM][SINGLE][".$userid."] Returning...");
    $log->debug(" ");
});

$app->get("/amr/form/history", function () use ($app) {
    $req = $app->request;
    $res = $app->response;
    $log = $app->logger;

    $log->debug("HTTP/GET /amr/form/history");
    Util::check_if_user_logged_in($req, $log);
    if ($req->permissions[PRIVILEGE_VIEW_FORM_1A] == false) {
        // Handle requests from users without the proper permissions
        $log->error("The user does not have the correct permissions.");
        throw new HTTPForbidden("You do not have the proper user permissions", "Permissions Error");
    }

    $userid = $req->user_id;
    $tracking_num = Util::get_required_param($req, 'tracking_num', $log);
    $log->debug("[AMR][FORM][HISTORY][".$userid."] Searching for AMR form history " . $tracking_num . "...");

    $forms = $app->db->get_amr_history($tracking_num);
    if (!$forms) {
        // Handle no amr history found
        $log->debug("[AMR][FORM][HISTORY][".$userid."] History for AMR form " . $tracking_num . " was NOT found.");
    }
    else {
        $log->debug("[AMR][FORM][HISTORY][".$userid."] AMR form history for " . $tracking_num . " was found");
    }

    foreach ($forms as &$form) {
        $timestamp = $form["history_date"];
        $log->debug("[AMR][FORM][HISTORY][".$userid."] Checks AMR form history for " . $tracking_num . " at time" .$timestamp);

        // Checking if user can access
        if ($app->domain === EMS_DOMAIN_INTERNAL) { // Internal
            $user = $app->db->get_user_by_user_id($userid);
            $user_role = $user["user_role"];
            // Check to see if they should be allowed to see this particular form
            if (($user_role == EMS_ADMIN) || // EMS Admin can see no matter what
                ($user_role == EMS_DCISE_MAC && $form["directorate"] == DIRECTORATE_DCISE) || // DCISE MAC can see only for DCISE submissions
                ($user_role == EMS_AG_MAC && $form["directorate"] == DIRECTORATE_AG)) { // AG can see only for AG submissions
                // User can view this exam
            }
            else {
                $log->error("[AMR][FORM][HISTORY][".$userid."] User has role " . $user_role . ". The form is for directorate " . $form["directorate"] . ". The user is not permitted to view this AMR submission history.");
                continue;
            }
        }
        else { // External
            $owner = $form["owner"];
            $members = in_array($owner, $app->db->get_service_ids()) ? $app->db->get_service_members_user_ids($owner) : array();

            // Handle requests for a submission that the user is not supposed to have access to
            if ($owner != $userid && !in_array($userid, $members) && !is_dc3_user_from_same_directorate($app->db, $tracking_num, $userid)) {
                $log->error("The user is attempting to view a form history that is not theirs.");
                continue;
            }
        }

        // Add service details if the AMR was submitted by a service
        if ($form['submitted_source'] == 'SERVICE') {
            $service = $app->db->get_service_by_tracking_number($tracking_num);
            $service["service_created_timestamp"] = Util::createDateTime($service["service_created_timestamp"]);
            $service['service_members'] = $app->db->get_service_members($service['service_id']);
            $form = array_merge($form, $service);
        }

        if (array_key_exists("submit_date", $form)) {
            $form["submit_date"] = Util::createDateTime($form["submit_date"]);
        }
    }
    $response_body['amr'] = $forms;
    $response_body['amr_mappings'] = $app->settings['amr_mappings'];
    $response_body['amr_resubmission_interval_days'] = $app->settings['amr_resubmission_interval_days'];

    $res->setBody(json_encode($response_body));
    $log->debug("[AMR][FORM][HISTORY][".$userid."] Returning...");
    $log->debug(" ");
});


/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
* Resubmit
* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
* Resubmits an amr to Nutcracker be rerun      $app->resubmit_amr_form()
* Creates an Empty Directory in    /opt/testmnt/amr_submission/
* Named After the Created Form ID      mkdir($target_dir, 0777, true)
*
* URL      : /amr/form/resubmit
* Method   : POST
*
* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
$app->post("/amr/form/resubmit", function () use ($app) {
    $req = $app->request;
    $res = $app->response;
    $log = $app->logger;
    $response_body = array();

    $log->debug("HTTP/POST /amr/form/resubmit");
    $userid = $req->user_id;
    $amr_limit =  $app->settings["amr_submit_limit"];
    $amr_count = $app->db->count_submitted_amrs($userid);
    if ($amr_count >= $amr_limit) {
        $log->error("The user currently has at least ".$amr_limit." AMRs waiting for results");
        throw new HTTPBadRequest("You currently have at least ".$amr_limit." AMRs already submitted and waiting for results. Please wait until one of them has received its results before attempting to resubmit again.", "Too Many AMR Submissions Pending");
    }

    $submission_directory =  $app->settings["submit_path"];
    $tracking_num = Util::get_required_param($req, 'tracking_num', $log);
    $log->debug("/amr/form/resubmit checking passwords");
    $passwords = $req->params('passwords');

    $log->debug("/amr/form/resubmit checking passwords: " . print_r($passwords, true));
    $triage_id = $app->db->get_amr_triage_id($tracking_num);
    $directorate_name = $app->db->get_user_directorate($userid);
    $target_dir = $submission_directory . DIRECTORY_SEPARATOR . $triage_id;

    $log->debug("[AMR][FORM][RESUBMIT][".$userid."] Attempting to create directory: " . print_r($target_dir, true));
    if (!mkdir($target_dir, 0777, true)) {
        $log->error("[AMR][FORM][RESUBMIT][".$userid."] The directory could not be created!");
        throw new HTTPBadRequest("This AMR has already been resubmitted and is waiting for results. If this persists Please contact EMS Support.", "Multiple Resubmission Error");
    }
    chmod($target_dir, 0760);
    $log->debug("[AMR][FORM][RESUBMIT][".$userid."] The directory was created!");


    $log->debug("[AMR][FORM][RESUBMIT][".$userid."] Creating the .done file: " . $target_dir . ".done");
    $resource = fopen($target_dir . ".done", "w");
    if (!$resource) {
        $log->error("Unable to create the .done file, but the directory has been created. Throwing HTTPServiceUnavailable.");
        throw new HTTPServiceUnavailable("An error occurred during amr resubmission. Please contact EMS Support.", "Error");
    }

    $log->debug("[AMR][FORM][RESUBMIT][".$userid."] Writing the form and submitter information to the .done file.");
    $log->debug("[AMR][FORM][RESUBMIT][".$userid."] Writing done_data with " . $tracking_num . " and " . $directorate_name);
    $done_data = array("AMR_resubmit" => True, "tracking_num" => $tracking_num, "directorate" => $directorate_name);
    $log->debug("[AMR][FORM][RESUBMIT][".$userid."] Checking to write passwords");
    if (isset($passwords) && count($passwords) > 0) {
        $done_data['passwords'] = $passwords;
    }
    $encoded_done_data = json_encode($done_data);
    $log->debug("[AMR][FORM][RESUBMIT][".$userid."] Encoded data is " . $encoded_done_data);
    $write_result = fwrite($resource, $encoded_done_data);
    if ($write_result === FALSE || $write_result < strlen($encoded_done_data)) {
        fclose($resource);
        $log->error("Unable to write the resubmit details to the .done file. Throwing HTTPServiceUnavailable.");
        throw new HTTPServiceUnavailable("An error occurred during amr resubmission. Please contact EMS Support.", "Error");
    }
    fclose($resource);
    $log->debug("[AMR][FORM][RESUBMIT][".$userid."] successfully completed done file.");
});