ALTER TABLE ems.ems_xref_service_users ADD CONSTRAINT ems_xref_service_users_fk1 FOREIGN KEY (created_by)
      REFERENCES ems_data_app_users (user_id);

ALTER TABLE ems.ems_xref_service_users ADD CONSTRAINT ems_xref_service_users_fk2 FOREIGN KEY (user_id)
      REFERENCES ems_data_app_users (user_id);

ALTER TABLE ems.ems_xref_service_users ADD CONSTRAINT ems_xref_service_users_fk3 FOREIGN KEY (service_id)
      REFERENCES ems_data_service_accounts (service_id);

ALTER TABLE ems.ems_xref_service_users ADD CONSTRAINT ems_xref_service_users_fk4
FOREIGN KEY (service_role_id) REFERENCES ems.ems_ref_service_roles (service_role_id);