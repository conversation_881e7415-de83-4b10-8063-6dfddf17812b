ALTER TABLE ems.ems_data_form1a ADD CONSTRAINT createdby_fk1 FOREIGN KEY (created_by)
      REFERENCES ems_data_app_users (user_id);

ALTER TABLE ems.ems_data_form1a ADD CONSTRAINT ems_data_form1a_fk3 FOREIGN KEY (submission_status)
      REFERENCES ems_ref_status (id);

ALTER TABLE ems.ems_data_form1a ADD CONSTRAINT form1a_approver_fk1 FOREIGN KEY (form1a_approver)
      REFERENCES ems_data_app_users (user_id);

ALTER TABLE ems.ems_data_form1a ADD CONSTRAINT dcfl_tracking_num_uk1 UNIQUE (dcfl_tracking_num);

ALTER TABLE ems.ems_data_form1a ADD CONSTRAINT ems_data_form1a_icf_type_id_fk FOREIGN KEY (icf_type_id)
      REFERENCES ems_ref_icf_types (icf_type_id);

ALTER TABLE ems.ems_data_form1a ADD CONSTRAINT ems_data_form1a_primary_analyst_id_fk FOREIGN KEY (primary_analyst_id)
      REFERENCES ems_data_app_users (user_id);

ALTER TABLE ems.ems_data_form1a ADD CONSTRAINT ems_data_form1a_submitter_company_id_fk FOREIGN KEY (submitter_company_id)
      REFERENCES ems_ref_companies (company_id);
