CREATE OR REPLACE TRIGGER aud_ems_data_exam_reports
  AFTER UPDATE OR DELETE
  ON ems.ems_data_exam_reports
  FOR EACH ROW
BEGIN
IF UPDATING THEN
    EMS.PKG_AUDIT.CHECK_VALUE( 'ems_data_exam_reports', 'email_sent', :NEW.form1a_id, :NEW.email_sent, :OLD.email_sent);

ELSIF DELETING THEN
    EMS.PKG_AUDIT.CHECK_VALUE( 'ems_data_exam_reports', 'form1a_id', :OLD.form1a_id, :OLD.form1a_id);
    EMS.PKG_AUDIT.CHECK_VALUE( 'ems_data_exam_reports', 'email_sent', :OLD.form1a_id, :OLD.email_sent);

 END IF;
END;

