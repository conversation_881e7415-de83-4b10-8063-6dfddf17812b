GRANT SELECT, UPDA<PERSON>, INSERT, DELETE, <PERSON><PERSON><PERSON><PERSON><PERSON> ON TABLE ems.app_user_sessions to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_audit_table to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIG<PERSON>R ON TABLE ems.ems_data_amp_triage to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_amp_triage_details to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_amp_triage_history to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_amr_passwords to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_announcements to :acct_name;
GRANT SELECT, UPDA<PERSON>, INSERT, DELETE, <PERSON><PERSON><PERSON><PERSON><PERSON> ON TABLE ems.ems_data_api_tokens to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_app_users to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_certificate_information to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_deleted_amp_triage to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_deleted_form1a to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_emails TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_exam_passwords to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_exam_reports to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_form1a to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_form1 to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_service_accounts to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_dc3_directorates to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_err_sql_logs to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_invalid_api_attempts to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_invalid_login_attempts to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_ref_companies to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_ref_examination_process to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_ref_examination_types to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_ref_organizations to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_ref_role_permissions to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_ref_status to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_ref_user_roles to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_ref_classification_types to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_user_login_history to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_user_permissions to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_amp_triage_files to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_amp_triage_derived_files to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_company_email_domains to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_examtype_process to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_expired_passwords to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_service_users to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_submitted_media to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_php_logs TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_python_logs TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_ref_service_permissions TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_service_permissions TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_vthunt TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_vthunt_files TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_xref_vthunt_notifications TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_upload_link TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_data_link_history TO :acct_name;

GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_amp_triage to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_amp_triage_history to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_exam_status to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_examtype_process to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_exam_types to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_form1a_data to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_form1_data to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_invalid_api_attempts to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_exam_reports to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_malware_submission_by_directorate to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_organizations to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_ref_status to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_service_token to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_service_user to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_submitted_media to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_triage_derived_media to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_triage_media to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_user_all_passwords to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_user_info to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_userpass_acct to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_user_role_permissions to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_user_roles to :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_php_logs TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_python_logs TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_err_sql_logs TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_dc3_directorates TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_service_permissions TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_ref_service_permissions TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.vw_vthunt_notifications TO :acct_name;
GRANT SELECT, UPDATE, INSERT, DELETE, TRIGGER ON TABLE ems.ems_ref_icf_types TO :acct_name;

GRANT EXECUTE ON PACKAGE ems.custom_types TO :acct_name;
GRANT EXECUTE ON PACKAGE ems_app_user TO :acct_name;
GRANT EXECUTE ON PACKAGE ems_global_definitions TO :acct_name;
GRANT EXECUTE ON PACKAGE ems_insert TO :acct_name;
GRANT EXECUTE ON PACKAGE ems_update TO :acct_name;
GRANT EXECUTE ON PACKAGE pkg_audit TO :acct_name;
GRANT EXECUTE ON PACKAGE pkg_utility TO :acct_name;

GRANT ALL ON SEQUENCE app_user_sessions_session_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_audit_table_audit_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_data_amp_triage_triage_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_data_amp_triage_details_triage_details_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_data_amp_triage_history_history_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_data_amr_passwords_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_data_api_tokens_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_data_exam_passwords_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_data_form1a_form1a_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_data_form1_form1_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_data_service_accounts_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_dc3_directorates_dc3_directorate_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_dcfl_tracking_num to :acct_name;
GRANT ALL ON SEQUENCE ems_err_sql_logs_sql_error_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_invalid_login_attempts_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_ref_examination_process_exam_process_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_ref_examination_types_examination_type_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_ref_organizations_organization_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_ref_role_permissions_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_ref_user_roles_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_ref_user_status_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_user_login_history_login_history_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_xref_amp_triage_files_xref_pk_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_xref_examtype_process_type_process_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_xref_expired_passwords_expired_pw_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_xref_service_users_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems_xref_submitted_media_submitted_media_id_seq to :acct_name;
GRANT ALL ON SEQUENCE user_permissions_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems.ems_php_logs_id_seq TO :acct_name;
GRANT ALL ON SEQUENCE ems.ems_python_logs_id_seq TO :acct_name;
GRANT ALL ON SEQUENCE ems.ems_xref_service_permissions_id_seq TO :acct_name;
GRANT ALL ON SEQUENCE ems.ems_data_emails_email_id_seq TO :acct_name;
GRANT ALL ON SEQUENCE ems.ems_invalid_api_attempts_id_seq TO :acct_name;
GRANT ALL ON SEQUENCE ems.ems_xref_amp_triage_derived_files_file_id_seq TO :acct_name;
GRANT ALL ON SEQUENCE ems.ems_xref_vthunt_vthunt_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems.ems_xref_vthunt_files_file_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems.ems_xref_vthunt_notifications_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems.ems_ref_companies_company_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems.ems_xref_company_email_domains_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems.ems_data_upload_link_url_audit_id_seq to :acct_name;
GRANT ALL ON SEQUENCE ems.ems_data_link_history_history_id_seq to :acct_name;

GRANT SELECT ON SEQUENCE app_user_sessions_session_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_audit_table_audit_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_data_amr_passwords_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_data_amp_triage_triage_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_data_amp_triage_details_triage_details_id_seq to ems_read;
GRANT SELECT ON SEQUENCE ems_data_amp_triage_history_history_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_data_exam_passwords_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_data_form1a_form1a_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_data_form1_form1_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_dc3_directorates_dc3_directorate_id_seq TO ems_read;
GRANT ALL ON SEQUENCE ems_err_sql_logs_sql_error_id_seq to ems_read;
GRANT SELECT ON SEQUENCE ems_invalid_login_attempts_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_ref_examination_process_exam_process_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_ref_examination_types_examination_type_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_ref_organizations_organization_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_ref_role_permissions_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_ref_user_roles_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_user_login_history_login_history_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_xref_amp_triage_files_xref_pk_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_xref_examtype_process_type_process_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_xref_expired_passwords_expired_pw_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_xref_service_users_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_xref_submitted_media_submitted_media_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_xref_vthunt_vthunt_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_xref_vthunt_files_file_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_xref_vthunt_notifications_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_ref_companies_company_id_seq to ems_read;
GRANT SELECT ON SEQUENCE ems_xref_company_email_domains_id_seq to ems_read;
GRANT SELECT ON SEQUENCE ems_data_upload_link_url_audit_id_seq TO ems_read;
GRANT SELECT ON SEQUENCE ems_data_link_history_history_id_seq TO ems_read;
