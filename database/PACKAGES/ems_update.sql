﻿-- Package: ems_update

-- DROP PACKAGE ems_update;

CREATE OR REPLACE PACKAGE ems.ems_update
AUTHID CURRENT_USER
IS

 user_id bigint;
 --See Package, 'EMS.EMS_GLOBAL_DEFINITIONS' for the codes that correspond to the following variables
 --Password Requirement/Account Creation Fail: 100-level and 200-level class error codes

 g_e_invalid_username integer;
 g_e_admin_lock integer;
 g_e_pw_change_24hrs integer;
 g_e_account_locked integer;
 e_invalid_user EXCEPTION;
 e_invalid_service EXCEPTION;
 e_not_exist EXCEPTION;

 g_strsql ems.custom_types.query_var;
 g_valid_param integer;
 g_row_cnt integer;

 FUNCTION upd_selectable_exam_types(p_user_id bigint, p_exam_type_id_i integer, p_exam_type_i character varying, p_exam_comment_i character varying) RETURN integer;
 FUNCTION upd_selectable_exam_process(p_user_id bigint, p_exam_process_id_i integer, p_exam_process_name_i character varying, p_process_desc character varying) RETURN integer;
 FUNCTION upd_selectable_organizations(p_user_id bigint, p_org_id_i integer, p_org_name_i character varying, p_dc3_direct_id_i integer) RETURN integer;
 FUNCTION upd_selectable_roles(p_user_id bigint, p_role_id_i integer, p_role_name_i character varying, p_role_desc_i character varying) RETURN integer;
 FUNCTION upd_selectable_permissions(p_user_id bigint, p_perm_id_i integer, p_perm_short_name_i character varying, p_user_perm_i character varying, p_perm_desc_i character varying) RETURN integer;
 FUNCTION upd_selectable_status(p_user_id bigint, p_status_id_i integer, p_status_i character varying) RETURN integer;
 FUNCTION upd_approve_user_role_status(p_status_approver_user_id bigint, p_user_to_approve_id bigint, p_status_id_i integer, p_user_role_i integer, p_user_email_v character varying, p_org_id bigint, p_company_id integer) RETURN integer;
 FUNCTION upd_user_registration(p_user_id bigint, p_fname_i character varying, p_lname_i character varying, p_mi_i character varying, p_email_add_i character varying, p_phone_i character varying, p_org_id_i integer) RETURN integer;
 FUNCTION upd_certificate_information(p_user_id bigint, p_subject_dn character varying, p_issuer character varying, p_serial_number character varying, p_valid timestamp without time zone, p_expires timestamp without time zone) RETURN integer;
 FUNCTION upd_dc3_directorate(p_user_id bigint, p_directorate_id_i integer, p_direct_name_i character varying) RETURN integer;
 FUNCTION upd_approve_form1a(p_approver_user_id bigint, p_form1a_id_i integer, p_status_i integer, p_rejection_reason character varying) RETURN integer;
 FUNCTION upd_auto_accept_form1a(p_user_id bigint, p_form1a_id_i integer, p_status_i integer) RETURN integer;
 FUNCTION upd_updateall_form1a(p_user_id bigint, p_exam_type_i integer, p_init_submission_i character varying, p_dcfl_case_no_i character varying, p_description_i clob, p_form1a_id_i integer, p_initial_case_num_i character varying, p_additional_comments_i character varying, p_subject_i character varying, p_partner_name_i character varying, p_partner_location_i character varying, p_dcfl_tracking_no_i character varying, p_sei_release_authorized boolean, p_sei_generate_yara_signature boolean, p_test_submission boolean, p_high_priority boolean, p_icf_type integer, p_primary_analyst_id bigint, p_on_behalf_of boolean, p_submitter_first_name text, p_submitter_last_name text, p_submitter_email text, p_submitter_phone text, p_submitter_company_id integer) RETURN integer;
 FUNCTION upd_form1a_status(p_user_id bigint, p_form1a_id_i integer, p_submission_status_id_i integer) RETURN integer;
 FUNCTION upd_cims_details(p_user_id bigint, p_form1a_id integer, p_assignee character varying, p_rejection_reason character varying) RETURN integer;
 FUNCTION upd_form1a_additional_comments(p_user_id bigint, p_form1a_id_i integer, p_additional_comments_i clob) RETURN integer;
 FUNCTION upd_form1a_dcfl_tracking_num(p_user_id bigint, p_form1a_id_i integer) RETURN character varying;
 FUNCTION upd_form1_tracking_number(p_form1_id_i integer, p_tracking_number_i character varying, p_agency_case_num_i character varying) RETURN integer;
 FUNCTION upd_form1_status(p_form1_id_i integer, p_submission_status_id_i integer) RETURN integer;
 FUNCTION upd_user_password(p_user_id bigint, p_username character varying, p_new_password character varying, p_forgot_password_flag boolean, p_password_salt character varying) RETURN integer;
 FUNCTION upd_unlock_username_account(p_user_id bigint, p_username character varying) RETURN integer;
 FUNCTION upd_account_status(p_user_id bigint,  p_account_status integer) RETURN integer;
 FUNCTION upd_triage_data(p_user_id bigint, p_triage_id integer, p_triage_report json, p_amr_version character varying) RETURN integer;
 FUNCTION upd_stix_data(p_user_id bigint, p_triage_id integer, p_stix_report json) RETURN integer;
 FUNCTION upd_triage_status(p_user_id bigint, p_triage_id_i integer, p_status_id_i integer, p_service_id integer) RETURN integer;
 FUNCTION upd_triage_number(p_user_id bigint, p_service_id integer, p_triage_id integer) RETURN character varying;
 FUNCTION upd_jira_issuekey_exam(p_tracking_num character varying, p_jira_issuekey character varying) RETURN integer;
 FUNCTION upd_jira_issuekey_amr(p_tracking_num character varying, p_jira_issuekey character varying) RETURN integer;
 FUNCTION upd_email_sent(p_email_id integer) RETURN integer;
 FUNCTION del_test_amr(p_triage_id bigint) RETURN integer;
 FUNCTION del_test_exam(p_form1a_id bigint) RETURN integer;
 FUNCTION deactivate_link(p_link_url uuid) RETURN integer;
 FUNCTION upd_link(p_link_url uuid, p_link_description character varying, p_submitter_first_name character varying, p_submitter_last_name character varying, p_submitter_email character varying, p_submitter_company_name character varying, p_subject_name_case_title character varying, p_expiration_date timestamp without time zone, p_submission_cap integer, p_case_or_icf_number character varying, p_icf_type_id integer) RETURN integer;

 -----
 --when update one field, other fields are null
 -----
 FUNCTION upd_service_account(p_user_id bigint, p_service_id integer, p_name character varying, p_description character varying, p_organization_id integer) RETURN integer;

 -----
 --when update one field, other fields are null
 -----
 FUNCTION upd_api_token(p_service_id integer, p_user_id bigint, p_token_hash character varying, p_token_salt character varying, p_expires_date timestamp without time zone) RETURN integer;
 FUNCTION upd_service_user_role(p_user_id bigint, p_service_id integer, p_role_id integer, p_operation_user_id bigint) RETURN integer;
 FUNCTION del_service_user(p_user_id bigint, p_service_id integer, p_operation_user_id bigint) RETURN integer;
 FUNCTION upd_ref_organization(p_user_id bigint, p_org_id integer, p_org_name character varying, p_directorate_id integer) RETURN integer;
 FUNCTION del_ref_organization(p_user_id bigint, p_org_id integer) RETURN integer;
 FUNCTION reassign_org_id(p_user_id bigint, p_old_org_id_i integer, p_new_org_id_i integer ) return integer;
 FUNCTION inactive_user_account() RETURN integer;
 FUNCTION del_xref_service_permission(p_service_id integer, p_service_permission_id integer, p_user_id bigint) RETURN integer;
 FUNCTION upd_exam_report_email_sent(p_formid integer, p_email_sent boolean, p_user_id bigint) RETURN integer;
 FUNCTION upd_announcement(p_announcement text) RETURN integer;
 FUNCTION del_vthunt(p_user_id bigint) RETURN integer;
 FUNCTION upd_company_email_domain(p_id integer, p_email_domain text) RETURN integer;
 FUNCTION del_company_email_domain(p_id integer) RETURN integer;
 FUNCTION upd_company_user(p_user_id bigint, p_company_id integer) RETURN integer;
 FUNCTION upd_company_disabled(p_company_id integer, p_disabled boolean) RETURN integer;
 FUNCTION upd_company_deleted(p_company_id integer, p_deleted boolean) RETURN integer;
 FUNCTION upd_submitted_media_ssdeep(p_submitted_file_id bigint, p_file_name character varying, p_ssdeep character varying) RETURN integer;

END ems_update;

CREATE OR REPLACE PACKAGE BODY ems.ems_update
IS

FUNCTION upd_selectable_exam_types(p_user_id bigint, p_exam_type_id_i integer, p_exam_type_i character varying, p_exam_comment_i character varying) RETURN integer IS

 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1 AND p_exam_type_id_i IS NOT NULL) THEN
    UPDATE ems.ems_ref_examination_types SET examination_type = p_exam_type_i, additional_comments = p_exam_comment_i WHERE examination_type_id = p_exam_type_id_i;
    g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN g_row_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
 BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_selectable_exam_types' ,user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_selectable_exam_types' ,user_id);
     RETURN 0;
   END;
 END;

 FUNCTION upd_selectable_exam_process(p_user_id bigint, p_exam_process_id_i integer, p_exam_process_name_i character varying, p_process_desc character varying) RETURN integer IS

 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1 AND p_exam_process_id_i IS NOT NULL) THEN
    UPDATE ems.ems_ref_examination_process SET exam_process_name = p_exam_process_name_i, exam_process_description = p_process_desc
      WHERE exam_process_id = p_exam_process_id_i;
    g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN g_row_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
 BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_selectable_exam_process' ,user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_selectable_exam_process',user_id);
     RETURN 0;
   END;
 END;

 FUNCTION upd_selectable_organizations(p_user_id bigint, p_org_id_i integer, p_org_name_i character varying, p_dc3_direct_id_i integer) RETURN integer IS

 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1 AND p_organization_id_i IS NOT NULL) THEN
    UPDATE ems.ems_ref_organizations SET organization_name = p_org_name_i, dc3_directorate_id = p_dc3_direct_id_i WHERE organization_id = p_org_id_i;
    g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN g_row_cnt;

EXCEPTION
 WHEN e_invalid_user THEN
 BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_selectable_organizations' ,user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_selectable_organizations' ,user_id);
     RETURN 0;
   END;
 END;

 FUNCTION upd_selectable_roles(p_user_id bigint, p_role_id_i integer, p_role_name_i character varying, p_role_desc_i character varying) RETURN integer IS

 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1 AND p_role_id_i IS NOT NULL) THEN
    UPDATE ems.ems_ref_user_roles SET user_role = p_role_name_i, description = p_role_desc_i WHERE id = p_role_id_i;
    g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN g_row_cnt;

EXCEPTION
  WHEN e_invalid_user THEN
 BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_selectable_roles' ,user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_selectable_roles' ,user_id);
     RETURN 0;
   END;
 END;

 FUNCTION upd_selectable_permissions(p_user_id bigint, p_perm_id_i integer, p_perm_short_name_i character varying, p_user_perm_i character varying, p_perm_desc_i character varying) RETURN integer IS

 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;


 IF (g_valid_param = 1 AND p_perm_id_i IS NOT NULL) THEN
    UPDATE ems.ems_user_permissions SET short_name = p_perm_short_name_i, user_permission = p_user_perm_i, description = p_perm_desc_i
      WHERE id = p_perm_id_i;
    g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN g_row_cnt;

EXCEPTION
 WHEN e_invalid_user THEN
 BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_selectable_roles',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_selectable_permissions',user_id);
     RETURN 0;
   END;
 END;

 FUNCTION upd_selectable_status(p_user_id bigint, p_status_id_i integer, p_status_i character varying) RETURN integer IS

 BEGIN
 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1 AND p_status_id_i IS NOT NULL) THEN
    UPDATE ems.ems_ref_status SET user_status = p_status_i WHERE id = p_status_id_i;
    g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN g_row_cnt;

EXCEPTION
 WHEN e_invalid_user THEN
 BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_selectable_status',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_selectable_status',user_id);
     RETURN 0;
   END;
 END;

 FUNCTION upd_approve_user_role_status(p_status_approver_user_id bigint, p_user_to_approve_id bigint, p_status_id_i integer, p_user_role_i integer, p_user_email_v character varying, p_org_id bigint, p_company_id integer) RETURN integer IS

 v_ag_org character varying(10) := 'DC3-AG';
 v_dcise_org character varying(100) := 'DC3-DCISE';
 v_org_id integer;

 v_old_status_id integer;
 v_old_role_id integer;
 v_status_name character varying(6) := 'Active';
 v_pending_reactivation_status_name character varying(20) := 'Pending Reactivation';
 v_active_status_id integer;
 v_pending_reactivation_status_id integer;


 BEGIN
 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_status_approver_user_id);
 user_id := ems.ems_app_user.getuser();

 IF (g_valid_param = 1) THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF (g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1) THEN

    select id into v_active_status_id from ems_ref_status where user_status = v_status_name;

    -- get id value for pending reactivation
    select id into v_pending_reactivation_status_id from ems_ref_status where user_status = v_pending_reactivation_status_name;

    select status_id, user_role_id into v_old_status_id, v_old_role_id from ems.ems_data_app_users where user_id = p_user_to_approve_id;

    -- does not update approved_timestamp if user was pending_reactivation
    IF  p_status_id_i =v_active_status_id
        and (p_status_id_i != coalesce(v_old_status_id, -1))
        and v_old_status_id != v_pending_reactivation_status_id
    THEN
       UPDATE ems.ems_data_app_users
        SET status_id = p_status_id_i, approved_timestamp=systimestamp, user_role_id = p_user_role_i, email_add = p_user_email_v,
          approved_by = p_status_approver_user_id, organization_id = p_org_id, company_id = p_company_id
        WHERE user_id = p_user_to_approve_id;
    ELSE
       UPDATE ems.ems_data_app_users
        SET user_role_id = p_user_role_i, email_add = p_user_email_v,
          approved_by = p_status_approver_user_id, organization_id = p_org_id, company_id = p_company_id
        WHERE user_id = p_user_to_approve_id;
    END IF;
    g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

 -- UPDATE the organization for AG MAC and DCISE MAC.
 ---DCISE
 IF (g_row_cnt = 1 AND p_user_role_i = 3) THEN

    SELECT organization_id INTO v_org_id
                 FROM ems.ems_ref_organizations
                 WHERE organization_name = v_dcise_org;

    UPDATE ems.ems_data_app_users SET organization_id = v_org_id WHERE user_id = p_user_to_approve_id;
 END IF;

 --AG
 IF (g_row_cnt = 1 AND p_user_role_i = 4) THEN

    SELECT organization_id INTO v_org_id
                 FROM ems.ems_ref_organizations
                 WHERE organization_name = v_ag_org;

    UPDATE ems.ems_data_app_users SET organization_id = v_org_id WHERE user_id = p_user_to_approve_id;
 END IF;

 RETURN g_row_cnt;

EXCEPTION
 WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_approve_user_role_status',user_id);
   RETURN 0;
 END;
 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_approve_user_role_status',user_id);
     RETURN 0;
   END;
END;

FUNCTION upd_user_registration(p_user_id bigint, p_fname_i character varying, p_lname_i character varying, p_mi_i character varying, p_email_add_i character varying, p_phone_i character varying, p_org_id_i integer) RETURN integer IS

 BEGIN
 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 /*Information about the user that only they are allowed to change.  Need to include an additional layer to ensure nobody else can update user's information*/
 IF (g_valid_param = 1 ) THEN
    UPDATE ems.ems_data_app_users
      SET first_name = p_fname_i, last_name = p_lname_i, middle_ini = p_mi_i, email_add = p_email_add_i, phone = p_phone_i, organization_id = p_org_id_i
    WHERE user_id = p_user_id;
    g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

RETURN g_row_cnt;

EXCEPTION
  WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_user_registration',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_user_registration',user_id);
     RETURN 0;
   END;
END;

FUNCTION upd_certificate_information(p_user_id bigint, p_subject_dn character varying, p_issuer character varying, p_serial_number character varying, p_valid timestamp without time zone, p_expires timestamp without time zone) RETURN integer IS

l_count		INTEGER;
e_no_match	EXCEPTION;
e_invalid_user	EXCEPTION;

BEGIN

--valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
g_valid_param := ems.ems_app_user.param_validation(p_user_id);
user_id := ems.ems_app_user.getuser();

IF (g_valid_param = 1) THEN
  IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
    ems.ems_app_user.setuser(p_user_id);
    user_id := ems.ems_app_user.getuser();
  END IF;
ELSIF (g_valid_param <> 1) THEN
  user_id := -99;
  RAISE e_invalid_user;
END IF;

-- Check to ensure the provided user id exists
SELECT COUNT(*) INTO l_count FROM ems.ems_data_certificate_information WHERE user_id = p_user_id;
IF (l_count = 0) THEN
   RAISE e_no_match;
ELSIF (l_count > 1) THEN
   RAISE e_invalid_user;
END IF;

UPDATE ems.ems_data_certificate_information
  SET subject_dn = p_subject_dn, issuer = p_issuer, serial = p_serial_number, valid = p_valid, expires = p_expires WHERE user_id = p_user_id;
g_row_cnt :=  SQL%ROWCOUNT;

RETURN g_row_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Invalid User ID. Count of user ID returned multiple values.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_certificate_information' ,-99);
     RETURN 0;
   END;
 WHEN e_no_match THEN
   BEGIN
     ems.pkg_utility.log_sql_error('No certificate information found for ID. Calling insert instead.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_certificate_information' ,-99);
     RETURN ems.ems_insert.ins_certificate_information(p_user_id, p_subject_dn, p_issuer, p_serial_number, p_valid, p_expires);
     END;
 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Unable to insert user''s certificate information.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_certificate_information' ,-99);
     RETURN 0;
   END;
 END;

 FUNCTION upd_dc3_directorate(p_user_id bigint, p_directorate_id_i integer, p_direct_name_i character varying) RETURN integer IS

 BEGIN
 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1 AND p_directorate_id_i IS NOT NULL) THEN
    UPDATE ems.ems_dc3_directorates SET directorate_name = p_direct_name_i WHERE dc3_directorate_id = p_directorate_id_i;
    g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN g_row_cnt;

EXCEPTION
 WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_dc3_directorate',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_dc3_directorate' ,user_id);
     RETURN 0;
   END;
END;

FUNCTION upd_approve_form1a(p_approver_user_id bigint, p_form1a_id_i integer, p_status_i integer, p_rejection_reason character varying) RETURN integer IS

l_associated_dc3_directorate integer;
l_dcfl_case_num ems.custom_types.query_var;
l_submitter bigint;

BEGIN
 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_approver_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_approver_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1) THEN

  SELECT dc3_directorate_owner, dcfl_case_num, created_by INTO l_associated_dc3_directorate, l_dcfl_case_num, l_submitter FROM ems.vw_form1a_data
  WHERE form1a_id = p_form1a_id_i;

  IF (l_associated_dc3_directorate = 4 AND l_dcfl_case_num = '' AND p_status_i IN (7, 8)) THEN
    --If the Directorate_ID = 4 (AG) and case number is empty and the status is approve or reject, generate the case number
    l_dcfl_case_num := ems.pkg_utility.get_ag_casenumber();

    UPDATE ems.ems_data_form1a
    SET form1a_approver = p_approver_user_id, form1a_approval_timestamp = SYSTIMESTAMP, status_updated_timestamp = SYSTIMESTAMP,
        submission_status = p_status_i, dcfl_case_num = l_dcfl_case_num, rejection_reason = p_rejection_reason
      WHERE form1a_id = p_form1a_id_i;
  ELSIF (l_associated_dc3_directorate = 1 AND l_dcfl_case_num = '' AND p_status_i IN (7, 8)) THEN
    --If the Directorate_ID = 1 (DCISE) and case number is empty and the status is approve or reject, generate the case number
    l_dcfl_case_num := ems.pkg_utility.get_dcise_casenumber(l_submitter, FALSE);

    UPDATE ems.ems_data_form1a
    SET form1a_approver = p_approver_user_id, form1a_approval_timestamp = SYSTIMESTAMP, status_updated_timestamp = SYSTIMESTAMP,
      submission_status = p_status_i, dcfl_case_num = l_dcfl_case_num, rejection_reason = p_rejection_reason
    WHERE form1a_id = p_form1a_id_i;
  ELSE
    UPDATE ems.ems_data_form1a
    SET form1a_approver = p_approver_user_id, form1a_approval_timestamp = SYSTIMESTAMP, status_updated_timestamp =SYSTIMESTAMP,
      submission_status = p_status_i, rejection_reason = p_rejection_reason
    WHERE form1a_id = p_form1a_id_i;
  END IF;
  /*This gives me a count of the number of rows were affected by the update statement above.*/
  g_row_cnt :=  SQL%ROWCOUNT;
 END IF;
 RETURN g_row_cnt;

EXCEPTION
  WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_approve_form1a',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_approve_form1a',user_id);
     RETURN 0;
   END;
END;

FUNCTION upd_auto_accept_form1a(p_user_id bigint, p_form1a_id_i integer, p_status_i integer) RETURN integer IS
-- Accept the form1a if the provided id exists and it active and matches the form submitter and user is under CFL

l_associated_dc3_directorate integer;
l_submitter bigint;

BEGIN
 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1) THEN

  SELECT dc3_directorate_owner, created_by INTO l_associated_dc3_directorate, l_submitter FROM ems.vw_form1a_data WHERE form1a_id = p_form1a_id_i;

  IF (l_associated_dc3_directorate = 3 AND l_submitter = p_user_id) THEN
    --If the Directorate_ID = 3 (CFL)
    UPDATE ems.ems_data_form1a
      SET form1a_approver = p_user_id, form1a_approval_timestamp = SYSTIMESTAMP, status_updated_timestamp = SYSTIMESTAMP,
        submission_status = p_status_i, primary_analyst_id = p_user_id
    WHERE form1a_id = p_form1a_id_i;

  END IF;
  /*This gives me a count of the number of rows were affected by the update statement above.*/
  g_row_cnt :=  SQL%ROWCOUNT;
 END IF;
 RETURN g_row_cnt;

EXCEPTION
  WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_primary_form1a',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_primary_form1a',user_id);
     RETURN 0;
   END;
END;

FUNCTION upd_updateall_form1a(p_user_id bigint, p_exam_type_i integer
      , p_init_submission_i character varying, p_dcfl_case_no_i character varying, p_description_i clob, p_form1a_id_i integer, p_initial_case_num_i character varying
      , p_additional_comments_i character varying, p_subject_i character varying, p_partner_name_i character varying, p_partner_location_i character varying, p_dcfl_tracking_no_i character varying
      ,p_sei_release_authorized boolean, p_sei_generate_yara_signature boolean, p_test_submission boolean, p_high_priority boolean, p_icf_type integer, p_primary_analyst_id bigint
      , p_on_behalf_of boolean, p_submitter_first_name text, p_submitter_last_name text, p_submitter_email text, p_submitter_phone text, p_submitter_company_id integer) RETURN integer IS

l_form1a_created_by BIGINT;
e_invalid_record_creator EXCEPTION;
l_edit_form1a_permission INTEGER;
l_user_role INTEGER;
l_form1_directorate INTEGER;
l_form1a_directorate_owner INTEGER;


BEGIN
 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 --Get the ID of user who created the form1a
 SELECT created_by INTO l_form1a_created_by FROM ems.ems_data_form1a WHERE form1a_id = p_form1a_id_i;

 --Get the permission ID to see if user id eligible to update the form1a
 SELECT COALESCE((SELECT COUNT(*)  INTO l_edit_form1a_permission FROM ems.vw_user_role_permissions WHERE user_id = p_user_id AND id = 3),0);

 --Get the user's assigned role
 SELECT user_role_id INTO l_user_role FROM ems.ems_data_app_users WHERE user_id = p_user_id;

 --Get the directorate that the form1a was submitted to
 SELECT dc3_directorate_owner  INTO l_form1a_directorate_owner FROM ems.vw_form1a_data WHERE form1a_id = p_form1a_id_i;

 IF (l_user_role <> 2) THEN   --user is not a customer
    IF (l_edit_form1a_permission < 1) THEN
       RAISE e_invalid_record_creator;
    ELSIF (l_user_role = 4 AND l_form1a_directorate_owner <> 4) THEN   --DC3-AG MAC id = 4; AG Directorate id 4 = DC3-AG
       RAISE e_invalid_record_creator;
    ELSIF (l_user_role = 3 AND l_form1a_directorate_owner <> 1) THEN   --DCISE MAC id = 3; DCISE Directorate id = 1
       RAISE e_invalid_record_creator;
    END IF;
 ELSE
    IF (l_form1a_created_by <> p_user_id) THEN
       RAISE e_invalid_record_creator;
    END IF;
 END IF;

  IF (g_valid_param = 1) THEN
    UPDATE ems.ems_data_form1a
      SET examination_type_id = p_exam_type_i, initial_submission = p_init_submission_i, dcfl_case_num = p_dcfl_case_no_i, investigation_description = p_description_i,
        initial_case_num = p_initial_case_num_i, additional_comments = p_additional_comments_i, name_of_subject = p_subject_i, partner_name = p_partner_name_i,
        partner_location = p_partner_location_i, dcfl_tracking_num = p_dcfl_tracking_no_i, sei_release_authorized = p_sei_release_authorized,
        sei_generate_yara_signature = p_sei_generate_yara_signature,
        test_submission = p_test_submission, high_priority = p_high_priority, icf_type_id = p_icf_type, primary_analyst_id = p_primary_analyst_id,
        on_behalf_of = p_on_behalf_of, submitter_first_name = p_submitter_first_name, submitter_last_name = p_submitter_last_name,
        submitter_email = p_submitter_email, submitter_phone = p_submitter_phone, submitter_company_id = p_submitter_company_id
    WHERE form1a_id = p_form1a_id_i;
    g_row_cnt :=  SQL%ROWCOUNT;
  END IF;

RETURN g_row_cnt;

EXCEPTION
  WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_updateall_form1a',user_id);
   RETURN 0;
 END;

 WHEN e_invalid_record_creator THEN
  BEGIN
   ems.pkg_utility.log_sql_error('User '||p_user_id ||' attempted to update form1a that they did not create. Form1a_ID = '||p_form1a_id_i ||'.',  substr(sqlerrm, 1, 3500),'ems_update','upd_updateall_form1a',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_updateall_form1a',user_id);
     RETURN 0;
   END;
END;

FUNCTION upd_form1a_status(p_user_id bigint, p_form1a_id_i integer, p_submission_status_id_i integer) RETURN integer IS

BEGIN
 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF(g_valid_param = 1) THEN
   UPDATE ems.ems_data_form1a SET submission_status = p_submission_status_id_i, status_updated_timestamp = now() WHERE form1a_id = p_form1a_id_i;
   g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

  RETURN g_row_cnt;

EXCEPTION
  WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_form1a_status',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_form1a_status',user_id);
     RETURN 0;
   END;

END;

FUNCTION upd_cims_details(p_user_id bigint, p_form1a_id integer, p_assignee character varying, p_rejection_reason character varying) RETURN integer IS

BEGIN
 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF(g_valid_param = 1) THEN
   UPDATE ems.ems_data_form1a SET cims_assignee = p_assignee, cims_rejection_reason = p_rejection_reason WHERE form1a_id = p_form1a_id;
   g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

  RETURN g_row_cnt;

EXCEPTION
  WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_cims_details',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_cims_details',user_id);
     RETURN 0;
   END;

END;

FUNCTION upd_form1a_additional_comments(p_user_id bigint, p_form1a_id_i integer, p_additional_comments_i clob) RETURN integer IS

BEGIN
 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF(g_valid_param = 1) THEN
   UPDATE ems.ems_data_form1a SET additional_comments = p_additional_comments_i WHERE form1a_id = p_form1a_id_i;
   g_row_cnt :=  SQL%ROWCOUNT;
 END IF;

  RETURN g_row_cnt;

EXCEPTION
  WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_form1a_status',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_form1a_status',user_id);
     RETURN 0;
   END;

END;

FUNCTION upd_form1a_dcfl_tracking_num(p_user_id bigint, p_form1a_id_i integer) RETURN character varying IS
    v_dcfl_tracking_num character varying;

BEGIN
    --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
    g_valid_param := ems.ems_app_user.param_validation(p_user_id);
    user_id := ems.ems_app_user.getuser();

    IF(g_valid_param = 1 )THEN
        IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
            ems.ems_app_user.setuser(p_user_id);
            user_id := ems.ems_app_user.getuser();
        END IF;
    ELSIF(g_valid_param != 1) THEN
        user_id := -99;
        RAISE e_invalid_user;
    END IF;

    IF(g_valid_param = 1) THEN
        SELECT * INTO v_dcfl_tracking_num FROM ems.pkg_utility.generate_dcfl_tracking_number();
        UPDATE ems.ems_data_form1a SET dcfl_tracking_num = v_dcfl_tracking_num WHERE form1a_id = p_form1a_id_i;
    END IF;

    RETURN v_dcfl_tracking_num;

    EXCEPTION
    WHEN e_invalid_user THEN
    BEGIN
        ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_form1a_dcfl_tracking_num',user_id);
        RETURN 0;
    END;

    WHEN OTHERS THEN
    BEGIN
        ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_form1a_dcfl_tracking_num',user_id);
        RETURN 0;
    END;

END;

FUNCTION upd_form1_tracking_number(p_form1_id_i integer, p_tracking_number_i character varying, p_agency_case_num_i character varying) RETURN integer IS
g_row_cnt integer;

BEGIN
  IF p_agency_case_num_i IS NOT NULL THEN
    UPDATE ems.ems_data_form1 SET tracking_number = p_tracking_number_i, agency_case_num = p_agency_case_num_i  WHERE form1_id = p_form1_id_i;
    g_row_cnt :=  SQL%ROWCOUNT;
  ELSE
    UPDATE ems.ems_data_form1 SET tracking_number = p_tracking_number_i WHERE form1_id = p_form1_id_i;
    g_row_cnt :=  SQL%ROWCOUNT;
  END IF;
RETURN g_row_cnt;

EXCEPTION
  WHEN OTHERS THEN
    BEGIN
      ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_form1_tracking_number',user_id);
      RETURN 0;
  END;
END;

FUNCTION upd_form1_status(p_form1_id_i integer, p_submission_status_id_i integer) RETURN integer IS

BEGIN
  UPDATE ems.ems_data_form1 SET submission_status = p_submission_status_id_i, status_updated_timestamp = CURRENT_TIMESTAMP WHERE form1_id = p_form1_id_i;
  g_row_cnt :=  SQL%ROWCOUNT;
RETURN g_row_cnt;

EXCEPTION
  WHEN OTHERS THEN
    BEGIN
      ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_form1_status',user_id);
      RETURN 0;
  END;
END;

FUNCTION upd_user_password(p_user_id bigint, p_username character varying, p_new_password character varying, p_forgot_password_flag boolean, p_password_salt character varying) RETURN integer IS

    l_account_status INTEGER := 2;       --(2 = The default status when user creates a login account)
    l_password_history_count INTEGER;
    l_count integer;
    e_invalid_username EXCEPTION;
    e_invalid_user EXCEPTION;
    e_password_duration EXCEPTION;
    l_password_duration INTEGER;
    l_forgot_pwd_reset_cnt INTEGER;

BEGIN
    --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
    g_valid_param := ems.ems_app_user.param_validation(p_user_id);
    user_id := ems.ems_app_user.getuser();

    IF(g_valid_param = 1 )THEN
       IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
          ems.ems_app_user.setuser(p_user_id);
           user_id := ems.ems_app_user.getuser();
       END IF;
    ELSIF(g_valid_param != 1) THEN
       user_id := -99;
       RAISE e_invalid_user;
    END IF;

    --Validate the given username
    SELECT COALESCE((SELECT COUNT(*) INTO l_count FROM ems.ems_user_login_history e WHERE e.user_id = p_user_id AND lower(e.user_name) = trim(lower(p_username))),0);

    IF (l_count < 1) THEN
       RAISE e_invalid_username;
    END IF;


    --Raise an error if the user's password was updated more than three times in the past 24-hours
    SELECT COUNT(*) INTO l_password_duration FROM ems_xref_expired_passwords WHERE password_is_expired >= NOW() - 1 AND password_is_expired <= NOW() AND user_id = p_user_id;

    IF (l_password_duration >= 3) THEN
        RAISE e_password_duration;
    END IF;

    --Delete the oldest password if the user has at least 4 passwords in the expired passwords table. This would make it the 5th
    SELECT COUNT(*) INTO l_password_history_count FROM ems_xref_expired_passwords WHERE user_id = p_user_id;

    IF (l_password_history_count >= 4) THEN
      DELETE FROM ems.ems_xref_expired_passwords e
        WHERE e.user_id = p_user_id AND lower(e.user_name) = trim(lower(p_username))
          AND e.password_is_expired = (
            SELECT MIN(password_is_expired) FROM (
              SELECT x.password_is_expired FROM ems.ems_xref_expired_passwords x
                WHERE x.user_id = p_user_id AND lower(x.user_name) = trim(lower(p_username)) order by password_is_expired desc limit 4));
    END IF;

    --Copy the password that is about to be updated into the expired passwords table
    INSERT INTO ems.ems_xref_expired_passwords(user_id, expired_user_password, user_name, password_is_expired, password_salt)
      SELECT j.user_id, j.user_password, j.user_name, COALESCE(j.password_is_expired,systimestamp), j.password_salt
      FROM ems.ems_user_login_history j WHERE j.user_id = p_user_id AND lower(j.user_name) = trim(lower(p_username));

    --Set new password data on ems_user_login_history
    UPDATE ems.ems_user_login_history
      SET user_password = p_new_password, password_created_updated = SYSTIMESTAMP, password_is_expired = NULL, password_salt = p_password_salt
      WHERE user_id = p_user_id AND lower(user_name) = trim(lower(p_username));

    ---set forgot_pwd_reset_cnt
    UPDATE ems.ems_user_login_history
      SET forgot_pwd_reset_cnt = case when p_forgot_password_flag = FALSE then 0 else forgot_pwd_reset_cnt+1 end
      WHERE user_id = p_user_id AND lower(user_name) = trim(lower(p_username));

    -- get forgot_pwd_reset_cnt
    SELECT forgot_pwd_reset_cnt INTO l_forgot_pwd_reset_cnt FROM ems.ems_user_login_history WHERE user_id = p_user_id AND lower(user_name) = trim(lower(p_username));

    --reset invalid login counter
    ems.ems_app_user.reset_failed_login_counter(p_username);

    RETURN l_forgot_pwd_reset_cnt;

EXCEPTION
    WHEN e_invalid_user THEN
    BEGIN
        ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_user_password',user_id);
        RETURN 0;
    END;

    WHEN e_invalid_username THEN
    BEGIN
        g_e_invalid_username := ems.ems_global_definitions.err_code_login_fail('g_e_invalid_username');
        ems.pkg_utility.log_sql_error('Invalid username. Code: '||g_e_invalid_username, substr(sqlerrm, 1, 3500), 'ems_update' ,'upd_user_password', p_user_id);
        RETURN g_e_invalid_username;
    END;

    WHEN e_password_duration THEN
    BEGIN
       g_e_pw_change_24hrs := ems.ems_global_definitions.err_code_password_fail('g_e_pw_change_24hrs');
       ems.pkg_utility.log_sql_error('You cannot update your password more than three times in 24 hours. Code: '||g_e_pw_change_24hrs, substr(sqlerrm, 1, 3500), 'ems_update' ,'upd_user_password', user_id);
       RETURN g_e_pw_change_24hrs;
    END;

    WHEN OTHERS THEN
    BEGIN
       ems.pkg_utility.log_sql_error('An error occurred while attempting to update user password. ' || substr(sqlerrm, 1, 3500), to_char(sqlcode), 'ems_update' ,'upd_user_password', p_user_id);
       RETURN -1;
    END;
END;



FUNCTION upd_unlock_username_account(p_user_id bigint, p_username character varying) RETURN integer IS

l_count integer;
l_account_status integer;
l_active_account integer := 2;
e_invalid_username EXCEPTION;
e_admin_lock EXCEPTION;

BEGIN

--Validate the given username/user_id
SELECT COALESCE((SELECT COUNT(*) INTO l_count FROM ems.ems_user_login_history e WHERE e.user_id = p_user_id AND lower(e.user_name) = trim(lower(p_username))),0);

IF (l_count < 1) THEN
   RAISE e_invalid_username;
END IF;

--Check the type of account lock. Valid codes are 18 (failed login attemps) or 19 (user attempted to update audit table)
SELECT account_status INTO l_account_status FROM ems.ems_user_login_history e WHERE e.user_id = p_user_id AND lower(e.user_name) = trim(lower(p_username));

--If the status is 19 (admin-lock), then only an EMS developer can unlock the user's account
IF (l_account_status = 19) THEN
  RAISE e_admin_lock;
END IF;

--Reset the user's failed login attempts to zero
UPDATE ems.ems_invalid_login_attempts SET delete_flag = 'Y' WHERE delete_flag = 'N' AND lower(user_name) = trim(lower(p_username));

--Unlock the user's account
UPDATE ems.ems_user_login_history SET account_status = l_active_account, password_is_expired = NULL WHERE user_id = p_user_id AND lower(user_name) = trim(lower(p_username));

RETURN 1;

EXCEPTION
WHEN e_invalid_username THEN
 BEGIN
  g_e_invalid_username := ems.ems_global_definitions.err_code_login_fail('g_e_invalid_username');
  ems.pkg_utility.log_sql_error('Invalid username. Code: '||g_e_invalid_username, substr(sqlerrm, 1, 3500), 'ems_update' ,'upd_unlock_username_account', p_user_id);
  RETURN g_e_invalid_username;
 END;

WHEN e_admin_lock THEN
 BEGIN
  g_e_admin_lock := ems.ems_global_definitions.err_code_login_fail('g_e_admin_lock');
  ems.pkg_utility.log_sql_error('Only application developers can unlock this account. Code: '||g_e_admin_lock, substr(sqlerrm, 1, 3500), 'ems_update' ,'upd_unlock_username_account', p_user_id);
  RETURN g_e_admin_lock;
 END;

WHEN OTHERS THEN
 BEGIN
  ems.pkg_utility.log_sql_error('An error occurred while attempting to update user password. ' || substr(sqlerrm, 1, 3500), to_char(sqlcode), 'ems_update' ,'upd_unlock_username_account', p_user_id);
  RETURN -1;
 END;

END;

FUNCTION upd_account_status(p_user_id bigint,  p_account_status integer) RETURN integer IS

    l_count integer;
    l_account_status integer;
    l_active_account integer := 2;

    e_not_pending_reactivation   EXCEPTION;

BEGIN
    IF p_account_status = (select id from ems.ems_ref_status where user_status ='Pending Reactivation') THEN
        --for Pending Reactivation only
        SELECT COALESCE((SELECT COUNT(*) INTO l_count FROM ems.ems_data_app_users WHERE user_id = p_user_id),0);
        IF (l_count < 1) THEN
           RAISE e_invalid_user;
        END IF;
        UPDATE ems.ems_data_app_users a SET a.status_id = p_account_status WHERE a.user_id = p_user_id;
    ELSE
        RAISE e_not_pending_reactivation;
    END IF;
    RETURN 1;

EXCEPTION
WHEN e_invalid_user THEN
 BEGIN
    ems.pkg_utility.log_sql_error('Invalid user_id:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_account_status',-99);
    RETURN 0;
 END;

 WHEN e_not_pending_reactivation THEN
 BEGIN
    ems.pkg_utility.log_sql_error('Not pending reactivation status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_account_status',-99);
    RETURN 0;
 END;

WHEN OTHERS THEN
 BEGIN
    ems.pkg_utility.log_sql_error('An error occurred while attempting to update user account status. ' || substr(sqlerrm, 1, 3500), to_char(sqlcode), 'ems_update' ,'upd_account_status', p_user_id);
    RETURN 0;
 END;
END;

FUNCTION upd_triage_data(p_user_id bigint, p_triage_id integer, p_triage_report json, p_amr_version character varying) RETURN integer IS
BEGIN
g_valid_param := ems.ems_app_user.param_validation(p_user_id);
user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

UPDATE ems.ems_data_amp_triage
SET triage_result = p_triage_report, triage_result_added_date = SYSTIMESTAMP, triage_result_uploaded_by = p_user_id, amr_version = p_amr_version
WHERE triage_id = p_triage_id;

RETURN 1;

EXCEPTION
WHEN OTHERS THEN
 BEGIN
  ems.pkg_utility.log_sql_error('Attempt at updating triage data caused an error. ' || substr(sqlerrm, 1, 3500), to_char(sqlcode), 'ems_update' ,'upd_triage_data', user_id);
  RETURN 0;
 END;

END;

FUNCTION upd_stix_data(p_user_id bigint, p_triage_id integer, p_stix_report json) RETURN integer IS
BEGIN
g_valid_param := ems.ems_app_user.param_validation(p_user_id);
user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1)THEN
   IF user_id = -99 THEN --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

UPDATE ems.ems_data_amp_triage SET stix_result = p_stix_report, stix_result_added_date = SYSTIMESTAMP, stix_result_uploaded_by = p_user_id
WHERE triage_id = p_triage_id;

RETURN 1;

EXCEPTION
WHEN OTHERS THEN
 BEGIN
  ems.pkq_utility.log_sql_error('Attempt at updating stix data caused an error. ' || substr(sqlerrm, 1, 3500), to_char(sqlcode), 'ems_update', 'upd_stix_data', user_id);
  RETURN 0;
 END;

END;

FUNCTION upd_triage_status(p_user_id bigint, p_triage_id_i integer, p_status_id_i integer, p_service_id integer) RETURN integer IS
 l_good_service integer :=0;
 l_service_id integer;
 l_row_cnt integer;
BEGIN
 if p_user_id is not null then
     --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
     g_valid_param := ems.ems_app_user.param_validation(p_user_id);
     user_id := ems.ems_app_user.getuser();

    IF(g_valid_param = 1 )THEN
       IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
           ems.ems_app_user.setuser(p_user_id);
           user_id := ems.ems_app_user.getuser();
       END IF;
    ELSIF(g_valid_param != 1) THEN
       user_id := -99;
       RAISE e_invalid_user;
    END IF;
 end if;

 if p_service_id is not null then
     ems.ems_app_user.setservice(p_service_id);
     l_service_id := ems.ems_app_user.getservice();
     if l_service_id != p_service_id then
        raise e_invalid_service;
     end if;
     l_good_service := 1;
 end if;

 IF(g_valid_param = 1 or l_good_service=1) THEN
   UPDATE ems.ems_data_amp_triage SET status_id = p_status_id_i, status_updated_timestamp = SYSTIMESTAMP WHERE triage_id = p_triage_id_i;
   l_row_cnt :=  SQL%ROWCOUNT;
 END IF;

  RETURN l_row_cnt;

EXCEPTION
  WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_triage_status',user_id);
   RETURN 0;
 END;

  WHEN e_invalid_service THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid service_id:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_triage_status',l_service_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update','upd_triage_status',user_id);
     RETURN 0;
   END;

END;

FUNCTION upd_triage_number(p_user_id bigint, p_service_id integer, p_triage_id integer) RETURN character varying IS
    v_triage_number character varying(19);
    l_id bigint;
    l_invalid_user EXCEPTION;
    l_invalid_service EXCEPTION;

BEGIN
    IF p_user_id IS NOT NULL THEN
        ems.ems_app_user.setuser(p_user_id);
        l_id := ems.ems_app_user.getuser();
        IF l_id <> p_user_id THEN
            raise l_invalid_user;
        END if;

        SELECT * FROM ems.pkg_utility.generate_triage_number() INTO v_triage_number;
        UPDATE ems.ems_data_amp_triage SET triage_number = v_triage_number WHERE triage_id = p_triage_id;
    END IF;

    IF p_service_id IS NOT NULL THEN
        ems.ems_app_user.setservice(p_service_id);
        l_id := ems.ems_app_user.getservice();
        IF l_id <> p_service_id THEN
            raise l_invalid_service;
        END IF;

        SELECT * FROM ems.pkg_utility.generate_triage_number() INTO v_triage_number;
        UPDATE ems.ems_data_amp_triage SET triage_number = v_triage_number WHERE triage_id = p_triage_id;
    END IF;

    RETURN v_triage_number;

    EXCEPTION

    WHEN l_invalid_service THEN
    BEGIN
        ems.pkg_utility.log_sql_error('Invalid service id. ' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_triage_number', l_id);
        RETURN 0;
    END;
    WHEN l_invalid_user THEN
    BEGIN
        ems.pkg_utility.log_sql_error('Invalid user id. ' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_triage_number', l_id);
        RETURN 0;
    END;
    WHEN OTHERS THEN
    BEGIN
        ems.pkg_utility.log_sql_error('An unknown error occurred' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'upd_triage_number', l_id);
        RETURN 0;
    END;
END;

FUNCTION upd_jira_issuekey_exam(p_tracking_num character varying, p_jira_issuekey character varying) RETURN integer IS
BEGIN
  UPDATE ems.ems_data_form1a SET dcise_jira_issuekey = p_jira_issuekey WHERE dcfl_tracking_num = p_tracking_num;
  RETURN 1;
  EXCEPTION
  WHEN OTHERS THEN
      BEGIN
          ems.pkg_utility.log_sql_error('Unable to update DCISE JIRA Issue Key' || substr(sqlerrm, 1, 3500), to_char(sqlcode), 'ems_update' ,'upd_jira_issuekey_exam', -99);
          RETURN 0;
      END;
END;

FUNCTION upd_jira_issuekey_amr(p_tracking_num character varying, p_jira_issuekey character varying) RETURN integer IS
BEGIN
  UPDATE ems.ems_data_amp_triage SET dcise_jira_issuekey = p_jira_issuekey WHERE triage_number = p_tracking_num;
  RETURN 1;
  EXCEPTION
  WHEN OTHERS THEN
      BEGIN
          ems.pkg_utility.log_sql_error('Unable to update DCISE JIRA Issue Key' || substr(sqlerrm, 1, 3500), to_char(sqlcode), 'ems_update' ,'upd_jira_issuekey_amr', -99);
          RETURN 0;
      END;
END;

FUNCTION del_test_amr(p_triage_id bigint) RETURN integer IS
   v_amr_year integer;
   v_amr_num  integer;
   v_triage_number character varying(19);
BEGIN
     user_id := ems.ems_app_user.getuser();

   --Delete records and extract metadata:
   select triage_number into v_triage_number from ems.ems_data_amp_triage where triage_id = p_triage_id;

   v_amr_year := cast((select substr(v_triage_number,5,4)) AS INTEGER);
   v_amr_num := cast((select substr(v_triage_number,10)) AS INTEGER);

   delete ems.ems_xref_amp_triage_derived_files
   where submitted_file_id in (
      select xref_pk from ems.ems_xref_amp_triage_files files
      join ems.ems_data_amp_triage amrs on files.triage_id = amrs.triage_id
      where amrs.triage_id = p_triage_id and test_submission = TRUE
   );

   delete ems.ems_xref_amp_triage_files
   where triage_id in (select triage_id from ems.ems_data_amp_triage where triage_id = p_triage_id and test_submission = TRUE);

   delete ems.ems_data_amr_passwords
   where triage_id in (select triage_id from ems.ems_data_amp_triage where triage_id = p_triage_id and test_submission = TRUE);

   delete ems.ems_data_amp_triage_details where triage_id = p_triage_id;

   delete ems.ems_data_amp_triage where triage_id = p_triage_id and test_submission = TRUE;

   insert into ems.ems_data_deleted_amp_triage(triage_id, triage_number, amr_year, amr_num, delete_date)
   values (p_triage_id, v_triage_number, v_amr_year, v_amr_num, systimestamp);

  RETURN 1;
EXCEPTION
 WHEN OTHERS THEN
   BEGIN
  ems.pkg_utility.log_sql_error('Attempt at deleting test triage data caused an error. ' || substr(sqlerrm, 1, 3500), to_char(sqlcode), 'ems_update' ,'del_test_amr', user_id);
  RETURN 0;
   END;
END;

FUNCTION del_test_exam(p_form1a_id bigint) RETURN integer IS
   v_exam_year integer;
   v_exam_num  integer;
   v_dcfl_tracking_num character varying(100);
BEGIN
   user_id := ems.ems_app_user.getuser();

   select dcfl_tracking_num into v_dcfl_tracking_num from ems.ems_data_form1a where form1a_id = p_form1a_id;

   v_exam_year := cast((select substr(v_dcfl_tracking_num,6,4)) AS INTEGER);
   v_exam_num := cast((select substr(v_dcfl_tracking_num,12)) AS INTEGER);

  delete from ems.ems_data_exam_reports
  where form1a_id in (select form1a_id from ems.ems_data_form1a where form1a_id = p_form1a_id and test_submission = TRUE);
  delete from ems.ems_xref_submitted_media
  where form1a_id in (select form1a_id from ems.ems_data_form1a where form1a_id = p_form1a_id and test_submission = TRUE);
  delete from ems.ems_data_exam_passwords
  where form1a_id in (select form1a_id from ems.ems_data_form1a where form1a_id = p_form1a_id and test_submission = TRUE);
  delete from ems.ems_data_form1a where form1a_id = p_form1a_id AND test_submission = TRUE;

   insert into ems.ems_data_deleted_form1a(form1a_id, dcfl_tracking_num, exam_year, exam_num, delete_date)
   values (p_form1a_id, v_dcfl_tracking_num, v_exam_year, v_exam_num, systimestamp);

  RETURN 1;
EXCEPTION
 WHEN OTHERS THEN
   BEGIN
  ems.pkg_utility.log_sql_error('Attempt at deleting test exam data caused an error. ' || substr(sqlerrm, 1, 3500), to_char(sqlcode), 'ems_update' ,'del_test_exam', user_id);
  RETURN 0;
   END;
END;

FUNCTION upd_service_account(p_user_id bigint, p_service_id integer, p_name character varying, p_description character varying, p_organization_id integer) RETURN integer
 IS
 v_cnt integer;
BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF(g_valid_param = 1) THEN

  UPDATE ems.ems_data_service_accounts
  SET name = COALESCE(p_name,name), description = COALESCE(p_description, description), organization_id = coalesce(p_organization_id,organization_id)
  WHERE service_id = p_service_id;
  v_cnt :=  SQL%ROWCOUNT;
 END IF;


 RETURN v_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_service_account',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update' ,'upd_service_account',user_id);
     RETURN 0;
   END;
END;

FUNCTION upd_api_token(p_service_id integer, p_user_id bigint, p_token_hash character varying, p_token_salt character varying, p_expires_date timestamp without time zone) RETURN integer
 IS
 v_cnt integer;
 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF(g_valid_param = 1) THEN

  UPDATE ems.ems_data_api_tokens
  SET token_hash = COALESCE(p_token_hash,token_hash), token_salt = COALESCE(p_token_salt, token_salt), expires_date = coalesce(p_expires_date,expires_date)
  WHERE service_id = p_service_id;
  v_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN v_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_api_token',user_id);
   RETURN 0;

 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update' ,'upd_api_token',user_id);
     RETURN 0;
   END;
END;

FUNCTION upd_service_user_role(p_user_id bigint, p_service_id integer, p_role_id integer, p_operation_user_id bigint) RETURN integer
 IS
 v_cnt integer;
 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_operation_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_operation_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF(g_valid_param = 1) THEN

  UPDATE ems.ems_xref_service_users SET service_role_id = p_role_id  WHERE service_id = p_service_id and user_id=p_user_id;
  v_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN v_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_service_user_role',user_id);
   RETURN 0;

 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update' ,'upd_service_user_role',user_id);
     RETURN 0;
   END;
END;
FUNCTION del_service_user(p_user_id bigint, p_service_id integer, p_operation_user_id bigint) RETURN integer
 IS
 v_cnt integer;
 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_operation_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_operation_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF(g_valid_param = 1) THEN

  delete ems.ems_xref_service_users WHERE service_id = p_service_id and user_id=p_user_id;
  v_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN v_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','del_service_user',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update' ,'del_service_user',user_id);
     RETURN 0;
   END;
END;

 FUNCTION upd_ref_organization(p_user_id bigint, p_org_id integer, p_org_name character varying, p_directorate_id integer) RETURN integer
 IS
 v_cnt integer;
 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF (g_valid_param = 1) THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF (g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1) THEN
  SELECT count(*) INTO v_cnt FROM ems.ems_ref_organizations WHERE organization_id = p_org_id;
  IF v_cnt = 0 THEN
     RAISE  e_not_exist;
  END IF;

  UPDATE ems.ems_ref_organizations SET organization_name = UPPER(p_org_name), directorate_id = p_directorate_id WHERE organization_id = p_org_id;
  v_cnt :=  SQL%ROWCOUNT;
 END IF;

 RETURN v_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','upd_ref_organization',user_id);
   RETURN 0;
 END;

 WHEN e_not_exist THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid record','The record to be updated does not exist','ems_update','upd_ref_organization',user_id);
   RETURN 0;
 END;
 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update' ,'upd_ref_organization',user_id);
     RETURN 0;
   END;

 END;

 FUNCTION del_ref_organization(p_user_id bigint, p_org_id integer) RETURN integer
 IS
 v_cnt integer;
 v_return_cnt integer;
 e_org_user_exist EXCEPTION;
 v_org_user_cnt integer;
 v_agency_id  integer;
 v_sub_agency_id   integer;
 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF (g_valid_param = 1) THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF (g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1) THEN
  SELECT count(*) INTO v_org_user_cnt FROM ems.ems_data_app_users WHERE organization_id = p_org_id;
  IF v_org_user_cnt > 0 THEN
     RAISE e_org_user_exist;
  END IF;

  SELECT count(*) INTO v_cnt FROM ems.ems_ref_organizations WHERE organization_id = p_org_id;
  IF v_cnt = 0 THEN
     RAISE  e_not_exist;
  END IF;

  DELETE ems.ems_ref_organizations WHERE organization_id = p_org_id;
  v_return_cnt :=  SQL%ROWCOUNT;

 END IF;

 RETURN v_return_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','del_ref_organization',user_id);
   RETURN 0;
 END;

 WHEN e_not_exist THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid record','The record to be deleted does not exist','ems_update','del_ref_organization',user_id);
   RETURN 0;
 END;

 WHEN e_org_user_exist THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Failed to delete','Could not delete because there are child records','ems_update','del_ref_organization',user_id);
   RETURN 0;
  END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update' ,'del_ref_organization',user_id);
     RETURN 0;
   END;
 END;

 FUNCTION reassign_org_id(p_user_id bigint, p_old_org_id_i integer, p_new_org_id_i integer) RETURN integer
 IS
    v_cnt integer;
 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF (g_valid_param = 1) THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF (g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF (g_valid_param = 1) THEN
  UPDATE ems.ems_data_app_users set organization_id = p_new_org_id_i WHERE organization_id = p_old_org_id_i;
  UPDATE ems.ems_data_service_accounts set organization_id = p_new_org_id_i WHERE organization_id = p_old_org_id_i;
  v_cnt :=  v_cnt + SQL%ROWCOUNT;
 END IF;

 RETURN v_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','reassign_org_id',user_id);
   RETURN 0;
 END;

 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update' ,'reassign_org_id',user_id);
     RETURN 0;
   END;
 END;

FUNCTION inactive_user_account() RETURN integer IS
    l_inactive_id integer := (select id from ems.ems_ref_status where user_status = 'Inactive');
    l_active_id integer := (select id from ems.ems_ref_status where user_status = 'Active');
 BEGIN
    ---never expire application user id *********
    update ems.ems_data_app_users set status_id = l_inactive_id
    where user_id != ********* and status_id = l_active_id and (last_accessed is null or systimestamp-interval '180 days' > last_accessed )
      and systimestamp-interval '180 days' >status_updated_timestamp;
    return 1;
 EXCEPTION
 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update' ,'inactive_user_account' ,-99);
     RETURN 0;
   END;
 END;

FUNCTION del_xref_service_permission(p_service_id integer, p_service_permission_id integer, p_user_id bigint) RETURN integer
 IS
 v_return_cnt integer;
 BEGIN

 --valid_param returns 1 if the user_id exists in the ems_data_app_users table AND their status is active
 g_valid_param := ems.ems_app_user.param_validation(p_user_id);
 user_id := ems.ems_app_user.getuser();

 IF(g_valid_param = 1 )THEN
   IF user_id = -99 THEN  --not call setuser or set it wrong before call the function
       ems.ems_app_user.setuser(p_user_id);
       user_id := ems.ems_app_user.getuser();
   END IF;
 ELSIF(g_valid_param != 1) THEN
   user_id := -99;
   RAISE e_invalid_user;
 END IF;

 IF(g_valid_param = 1) THEN

  DELETE ems.ems_xref_service_permissions WHERE service_id = p_service_id and service_permission_id= p_service_permission_id;
  v_return_cnt :=  SQL%ROWCOUNT;


 END IF;

 RETURN v_return_cnt;

 EXCEPTION
 WHEN e_invalid_user THEN
  BEGIN
   ems.pkg_utility.log_sql_error('Invalid user_id or account status:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update','del_xref_service_permission',user_id);
   RETURN 0;
 END;


 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Update Error:' || sqlcode,  substr(sqlerrm, 1, 3500),'ems_update' ,'del_xref_service_permission',user_id);
     RETURN 0;
   END;
END;

FUNCTION upd_exam_report_email_sent(p_formid integer, p_email_sent boolean, p_user_id bigint) RETURN integer IS
  l_user_id  bigint;
  l_invalid_user EXCEPTION;
  l_row_cnt integer;
BEGIN
    ems.ems_app_user.setuser(p_user_id);
    l_user_id := ems.ems_app_user.getuser();
    IF l_user_id <> p_user_id THEN
      raise l_invalid_user;
    END IF;

    update ems.ems_data_exam_reports set email_sent = p_email_sent where form1a_id = p_formid;

    l_row_cnt :=  SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN l_invalid_user THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Invalid user.' || sqlcode, substr(sqlerrm, 1, 3500) ,'ems_update', 'upd_exam_report_email_sent', -99);
    RETURN sqlcode;
  END;
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error updating exam report email_sent.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'upd_exam_report_email_sent', -99);
    RETURN sqlcode;
  END;
END;

FUNCTION upd_email_sent(p_email_id integer) RETURN integer IS
  l_row_cnt integer;
BEGIN
    UPDATE ems.ems_data_emails SET sent = TRUE where email_id = p_email_id;

    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error updating email sent flag.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'upd_email_sent', -99);
    RETURN sqlcode;
  END;
END;

FUNCTION upd_announcement(p_announcement text) RETURN integer IS
  l_row_cnt integer;
BEGIN
    UPDATE ems.ems_data_announcements SET announcement = p_announcement WHERE id = 1;
    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error updating announcement.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'upd_announcement', -99);
    RETURN sqlcode;
  END;
END;

FUNCTION del_vthunt(p_user_id bigint) RETURN integer IS
  l_row_cnt integer;
BEGIN
    DELETE FROM ems.ems_xref_vthunt WHERE user_id = p_user_id;
    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error deleting vthunt.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'del_vthunt', -99);
    RETURN sqlcode;
  END;
END;

FUNCTION upd_company_email_domain(p_id integer, p_email_domain text) RETURN integer IS
  l_row_cnt integer;
BEGIN
    UPDATE ems.ems_xref_company_email_domains SET email_domain = p_email_domain WHERE id = p_id;
    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error updating company email domain.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'upd_company_email_domain', -99);
    RETURN sqlcode;
  END;
END;

FUNCTION del_company_email_domain(p_id integer) RETURN integer IS
  l_row_cnt integer;
BEGIN
    DELETE FROM ems.ems_xref_company_email_domains WHERE id = p_id;
    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error deleting company email domain.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'del_company_email_domain', -99);
    RETURN sqlcode;
  END;
END;

FUNCTION upd_company_user(p_user_id bigint, p_company_id integer) RETURN integer IS
  l_row_cnt integer;
BEGIN
    UPDATE ems.ems_data_app_users SET company_id = p_company_id WHERE user_id = p_user_id;
    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error updating company user.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'upd_company_user', -99);
    RETURN sqlcode;
  END;
END;

FUNCTION upd_company_disabled(p_company_id integer, p_disabled boolean) RETURN integer IS
  l_row_cnt integer;
BEGIN
    UPDATE ems.ems_ref_companies SET disabled = p_disabled WHERE company_id = p_company_id;
    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error updating company disabled flag.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'upd_company_disabled', -99);
    RETURN sqlcode;
  END;
END;

FUNCTION upd_company_deleted(p_company_id integer, p_deleted boolean) RETURN integer IS
  l_row_cnt integer;
BEGIN
    UPDATE ems.ems_ref_companies SET deleted = p_deleted WHERE company_id = p_company_id;
    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error updating company deleted flag.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'upd_company_deleted', -99);
    RETURN sqlcode;
  END;
END;

FUNCTION upd_submitted_media_ssdeep(p_submitted_file_id bigint, p_file_name character varying, p_ssdeep character varying) RETURN integer IS
  l_row_cnt integer;
BEGIN
    UPDATE ems.ems_xref_amp_triage_files SET ssdeep = p_ssdeep WHERE xref_pk = p_submitted_file_id AND file_name = p_file_name;
    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error updating submitted media ssdeep.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'upd_submitted_media_ssdeep', -99);
    RETURN sqlcode;
  END;
END;

FUNCTION deactivate_link(p_link_url uuid) RETURN integer IS
  l_row_cnt integer;
BEGIN
    UPDATE ems.ems_data_upload_link SET link_active = false WHERE url_address = p_link_url;
    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error deactivation link.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'deactivate_link', -99);
    RETURN sqlcode;
  END;
END;


FUNCTION upd_link(p_link_url uuid, p_link_description character varying, p_submitter_first_name character varying, p_submitter_last_name character varying, p_submitter_email character varying, p_submitter_company_name character varying, p_subject_name_case_title character varying, p_expiration_date timestamp without time zone, p_submission_cap integer, p_case_or_icf_number character varying, p_icf_type_id integer) RETURN integer IS
  l_row_cnt integer;
BEGIN
    UPDATE ems.ems_data_upload_link SET link_description = p_link_description, submitter_first_name = p_submitter_first_name, submitter_last_name = p_submitter_last_name,
      submitter_email = p_submitter_email, submitter_company_name = p_submitter_company_name , subject_name_case_title = p_subject_name_case_title, expiration_date = p_expiration_date,
      submission_cap = p_submission_cap, case_or_icf_number  = p_case_or_icf_number, icf_type_id = p_icf_type_id WHERE url_address = p_link_url;

    l_row_cnt := SQL%ROWCOUNT;
    return l_row_cnt;
  EXCEPTION
  WHEN others THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error update link.' || sqlcode,  substr(sqlerrm, 1, 3500) ,'ems_update', 'upd_link', -99);
    RETURN sqlcode;
  END;
END;

END ems_update;

ALTER PACKAGE ems.ems_update OWNER TO ems_admin;
GRANT EXECUTE ON PACKAGE ems.ems_update TO ems_admin;
