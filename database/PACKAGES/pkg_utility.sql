﻿-- Package: ems.pkg_utility

-- DROP PACKAGE ems.pkg_utility;

CREATE OR REPLACE PACKAGE ems.pkg_utility
AUTHID CURRENT_USER
IS

l_strsql ems.custom_types.query_var;

PROCEDURE log_sql_error(p_message character varying, p_code character varying, p_sql_package character varying, p_sql_method character varying, p_logged_user_id bigint);
FUNCTION getfiscalyear(thedate timestamp without time zone) RETURN integer;
FUNCTION get_ag_casenumber() RETURN character varying;
FUNCTION get_dcise_casenumber(p_submitter bigint, p_on_behalf_of boolean) RETURN character varying;
FUNCTION generate_triage_number() RETURN character varying;
FUNCTION last_three_password_changes(p_user_id bigint) RETURN character varying[];
FUNCTION generate_dcfl_tracking_number() RETURN character varying;
FUNCTION generate_link_number() RETURN character varying;



END pkg_utility;

CREATE OR REPLACE PACKAGE BODY ems.pkg_utility
IS
PROCEDURE log_sql_error(p_message character varying, p_code character varying, p_sql_package character varying, p_sql_method character varying, p_logged_user_id bigint) IS
BEGIN
  INSERT
  INTO ems.ems_err_sql_logs(sql_message, sql_code, sql_package, sql_method, logged_user_id)
    VALUES(substr(p_message, 1, 4000), substr(p_code, 1, 4000), p_sql_package, p_sql_method, p_logged_user_id);

  /*EXCEPTION
    WHEN OTHERS THEN
      BEGIN
        utility.log_sql_error(SQLCODE, SUBSTR(SQLERRM, 1, 3500), 'UTILITY', 'log_error', gold_user.getuser());
      END;*/

END;

FUNCTION getfiscalyear(thedate timestamp without time zone) RETURN integer IS
  fiscalyear INTEGER;

BEGIN
  IF EXTRACT(MONTH FROM thedate) < 10 THEN
     fiscalyear := EXTRACT (YEAR FROM thedate);
  ELSE
     fiscalyear := EXTRACT(YEAR FROM thedate) + 1;
  END IF;

RETURN fiscalyear;

EXCEPTION
 WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error(to_char(SQLCODE), to_char(SUBSTR(SQLERRM, 1, 3500)), to_char('pkg_utility'), to_char('getfiscalyear'), -99);
    RETURN -1;
  END;

END;

FUNCTION get_ag_casenumber() RETURN character varying IS

l_fiscalyear  ems.custom_types.query_var;
vmax  ems.custom_types.query_var;
l_case_number ems.custom_types.query_var;
l_dc3_directorate_owner integer := 4; --AG
l_fy  ems.custom_types.query_var;

BEGIN

l_fiscalyear := substr(pkg_utility.getfiscalyear(SYSDATE), -2, 2);

l_fy := 'E' || l_fiscalyear || '-' || '%';

SELECT lpad(max(regexp_substr(d.dcfl_case_num, '[[:digit:]]+', 1, 2))::integer + 1, 3, '0') INTO vmax
FROM ems.ems_data_form1a d INNER JOIN ems.vw_user_info u ON u.user_id = d.created_by
WHERE u.directorate_id = l_dc3_directorate_owner AND regexp_substr(dcfl_case_num, '[[:digit:]]+', 1, 1) = l_fiscalyear AND dcfl_case_num LIKE l_fy;

IF vmax IS NOT NULL THEN
  l_case_number := 'E' || l_fiscalyear || '-' || vmax;
ELSE
  l_case_number := 'E' || l_fiscalyear || '-' || '001';
END IF;

RETURN l_case_number;

EXCEPTION
 WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error(to_char(SQLCODE), SQLERRM, to_char('pkg_utility'), to_char('get_ag_casenumber'), -99);
    RETURN -1;
  END;
END;

FUNCTION get_dcise_casenumber(p_submitter bigint, p_on_behalf_of boolean) RETURN character varying IS

l_fiscalyear ems.custom_types.query_var;
l_auto_inc ems.custom_types.query_var;
l_case_number ems.custom_types.query_var;
l_dc3_directorate_owner integer := 1; --DCISE
l_search_term ems.custom_types.query_var;
l_submitter_org ems.custom_types.query_var;

BEGIN

l_fiscalyear := pkg_utility.getfiscalyear(SYSDATE);

SELECT organization_name INTO l_submitter_org FROM ems.vw_user_info WHERE user_id = p_submitter
  UNION
SELECT organization_name FROM ems.vw_service_token s INNER JOIN ems.vw_organizations o ON o.id = s.organization_id WHERE service_id = p_submitter;

IF l_submitter_org = 'DC3-DCISE' AND NOT p_on_behalf_of THEN
  l_case_number := 'I';
ELSE
  l_case_number := 'E';
END IF;

l_search_term := l_case_number || '-' || l_fiscalyear || '-' || '%';

WITH case_numbers AS (
    -- case numbers from exams
    SELECT exam.dcfl_case_num AS case_number INTO l_auto_inc
    FROM ems.ems_data_form1a exam
    INNER JOIN ems.vw_user_info u ON u.user_id = exam.created_by
    WHERE u.directorate_id = l_dc3_directorate_owner AND dcfl_case_num LIKE l_search_term
    UNION
    -- case numbers from amrs submitted by users
    SELECT amr.case_or_icf_number AS case_number
    FROM ems.ems_data_amp_triage amr
    INNER JOIN ems.vw_user_info u ON u.user_id = amr.created_by
    WHERE u.directorate_id = l_dc3_directorate_owner AND case_or_icf_number LIKE l_search_term
    UNION
    -- case numbers from amrs submitted by services
    SELECT amr.case_or_icf_number AS case_number
    FROM ems.ems_data_amp_triage amr
    INNER JOIN ems.vw_service_token s ON s.service_id = amr.service_created_by
    INNER JOIN ems.vw_organizations o ON o.id = s.organization_id
    WHERE o.directorate_id = l_dc3_directorate_owner AND case_or_icf_number LIKE l_search_term
)
SELECT lpad(max(regexp_substr(case_number, '[[:digit:]]+', 1, 2))::integer + 1, 3, '0')
FROM case_numbers;

IF l_auto_inc IS NOT NULL THEN
  l_case_number := l_case_number || '-' || l_fiscalyear || '-' || l_auto_inc;
ELSE
  l_case_number := l_case_number || '-' || l_fiscalyear || '-' || '001';
END IF;

RETURN l_case_number;

EXCEPTION
 WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error(to_char(SQLCODE), SQLERRM, to_char('pkg_utility'), to_char('get_dcise_casenumber'), -99);
    RETURN -1;
  END;
END;

FUNCTION generate_triage_number() RETURN character varying IS

l_fiscalyear  ems.custom_types.query_var;
l_fy  ems.custom_types.query_var;
triage_number bigint;
l_num_rows integer;
l_triage_number ems.custom_types.query_var;
l_triage_digits character varying;

v_max_del_amr_num integer;

BEGIN

SELECT COALESCE((SELECT COUNT(*) FROM ems.ems_data_amp_triage),0) INTO l_num_rows;

l_fiscalyear := substr(pkg_utility.getfiscalyear(SYSDATE), -4) ||'-';

IF (l_num_rows) < 1 THEN
  l_triage_number := 'AMR-'|| l_fiscalyear || to_char(1,'fm0000000');
ELSE
 --Return the maximum ID reached for triage number in the current fiscal year
 SELECT max(regexp_substr(triage_number, '[[:digit:]]+', 1, 2)) INTO l_triage_digits
 FROM ems.ems_data_amp_triage WHERE regexp_substr(triage_number, '[[:digit:]]+', 1, 1) = substr(pkg_utility.getfiscalyear(SYSDATE), -4);

 select COALESCE(max(amr_num),0) into v_max_del_amr_num from ems.ems_data_deleted_amp_triage where amr_year = substr(pkg_utility.getfiscalyear(SYSDATE), -4);

 --If it's a new fiscal year, then "l_triage_digits" would be null. Restart the counter from 1 at the start of every fiscal year.
 IF (l_triage_digits IS NULL) THEN
    if v_max_del_amr_num !=0 then
       l_triage_number := 'AMR-'||l_fiscalyear || to_char((v_max_del_amr_num+1),'fm0000000');
    else
       l_triage_number := 'AMR-'||l_fiscalyear || to_char(1,'fm0000000');
    end if;
 ELSE
  if cast(l_triage_digits AS INTEGER) < v_max_del_amr_num then
     l_triage_digits := v_max_del_amr_num;
  end if;
  l_triage_digits := to_char((l_triage_digits + 1),'fm0000000');
  l_triage_number := 'AMR-'||l_fiscalyear || l_triage_digits;
 END IF;

END IF;

RETURN l_triage_number;

EXCEPTION
 WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error(to_char(SQLCODE), to_char(SUBSTR(SQLERRM, 1, 3500)), to_char('pkg_utility'), to_char('generate_triage_number'), -99);
    RETURN -1;
  END;
END;

FUNCTION last_three_password_changes(p_user_id bigint) RETURN character varying[] IS

l_most_recent_pw_change varchar(30);
l_2nd_most_recent_pw_change varchar(30);
l_3rd_most_recent_pw_change varchar(30);
l_last_three_pw_changes varchar[];
l_password_created_date varchar(30);
l_count integer;

BEGIN
    --Determine the number of times a user has updated their password
    SELECT COALESCE((SELECT count(*)  INTO l_count FROM ems.ems_xref_expired_passwords where user_id = p_user_id),0);

    --If the user doesn't have a password change history, then use the date the password was initially created
    SELECT userpass_account_created INTO l_password_created_date FROM ems.vw_userpass_acct WHERE user_id = p_user_id;

 CASE
   WHEN l_count = 0 THEN
     l_last_three_pw_changes := array_append(l_last_three_pw_changes, l_password_created_date);
     l_last_three_pw_changes := array_append(l_last_three_pw_changes,null);
     l_last_three_pw_changes := array_append(l_last_three_pw_changes,null);
   WHEN l_count = 1 THEN
     select max(password_is_expired) into l_most_recent_pw_change from ems.ems_xref_expired_passwords where user_id = p_user_id;

     l_last_three_pw_changes := array_append(l_last_three_pw_changes,l_most_recent_pw_change);
     l_last_three_pw_changes := array_append(l_last_three_pw_changes,l_password_created_date);
     l_last_three_pw_changes := array_append(l_last_three_pw_changes,null);
   WHEN l_count = 2 THEN
     select max(password_is_expired) into l_most_recent_pw_change from ems.ems_xref_expired_passwords where user_id = p_user_id;

     l_last_three_pw_changes := array_append(l_last_three_pw_changes,l_most_recent_pw_change);
     select min(password_is_expired) into l_2nd_most_recent_pw_change from
      (select password_is_expired from ems_xref_expired_passwords where user_id = p_user_id order by 1 desc limit 2);

     l_last_three_pw_changes := array_append(l_last_three_pw_changes, l_2nd_most_recent_pw_change);
     l_last_three_pw_changes := array_append(l_last_three_pw_changes, l_password_created_date);
   WHEN l_count > 2 THEN
     select max(password_is_expired) into l_most_recent_pw_change from ems.ems_xref_expired_passwords where user_id = p_user_id;
     l_last_three_pw_changes := array_append(l_last_three_pw_changes, l_most_recent_pw_change);

     select min(password_is_expired) into l_2nd_most_recent_pw_change from
      (select password_is_expired from ems_xref_expired_passwords where user_id = p_user_id order by 1 desc limit 2);

     l_last_three_pw_changes := array_append(l_last_three_pw_changes,l_2nd_most_recent_pw_change);
     select min(password_is_expired) into l_3rd_most_recent_pw_change from
      (select password_is_expired from ems_xref_expired_passwords where user_id = p_user_id order by 1 desc limit 3);

     l_last_three_pw_changes := array_append(l_last_three_pw_changes,l_3rd_most_recent_pw_change);
   ELSE
      NULL;
   END CASE;

RETURN l_last_three_pw_changes;

EXCEPTION
 WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error(to_char(SQLCODE), to_char(SUBSTR(SQLERRM, 1, 3500)), to_char('pkg_utility'), to_char('last_three_password_changes'), -99);
    RETURN array[-1];
  END;
END;

FUNCTION generate_dcfl_tracking_number() RETURN character varying IS

l_fiscalyear   	     ems.custom_types.query_var;
l_strsql ems.custom_types.query_var;
l_num_rows integer;
l_dcfl_tracking_number ems.custom_types.query_var;
l_tracking_number_digits ems.custom_types.query_var;

v_max_del_exam_num integer;
BEGIN

--user_id := ems.ems_app_user.getuser();
SELECT COALESCE((SELECT COUNT(*) FROM ems.ems_data_form1a),0) INTO l_num_rows;
l_fiscalyear := substr(pkg_utility.getfiscalyear(SYSDATE), -4) || '-E';

 select COALESCE(max(exam_num),0) into v_max_del_exam_num from ems.ems_data_deleted_form1a where exam_year = substr(pkg_utility.getfiscalyear(SYSDATE), -4);

IF (l_num_rows) < 1 THEN
   l_dcfl_tracking_number := 'DCFL-'||l_fiscalyear || to_char(1,'fm000000');
ELSE
--Return the maximum ID reached for DCFL Tracking Number in the current fiscal year
 SELECT max(regexp_substr(dcfl_tracking_num, '[[:digit:]]+', 1, 2)) INTO l_tracking_number_digits FROM ems.ems_data_form1a
 WHERE regexp_substr(dcfl_tracking_num, '[[:digit:]]+', 1, 1) = substr(pkg_utility.getfiscalyear(SYSDATE), -4);

 --If it's a new fiscal year, then "l_triage_digits" would be null. Restart the counter from 1 at the start of every fiscal year.
 IF (l_tracking_number_digits IS NULL) THEN
    if v_max_del_exam_num > 0 then
       l_dcfl_tracking_number := 'DCFL-'||l_fiscalyear || to_char((v_max_del_exam_num+1),'fm000000');
    else
      l_dcfl_tracking_number := 'DCFL-'||l_fiscalyear || to_char(1,'fm000000');
    end if;
 ELSE
    if cast(l_tracking_number_digits AS INTEGER) < v_max_del_exam_num then
         l_tracking_number_digits := v_max_del_exam_num;
    end if;
    l_tracking_number_digits := to_char((l_tracking_number_digits + 1),'fm000000');
    l_dcfl_tracking_number := 'DCFL-'||l_fiscalyear || l_tracking_number_digits;
 END IF;
END IF;

RETURN l_dcfl_tracking_number;

EXCEPTION
 WHEN OTHERS THEN
   BEGIN
     ems.pkg_utility.log_sql_error('Error while generating DCFL tracking number. ' || sqlcode,  substr(sqlerrm, 1, 3500) ,'pkg_utility' ,'generate_dcfl_tracking_number', -99);
     RETURN 0;
   END;
END;

FUNCTION generate_link_number() RETURN character varying IS

l_fiscalyear  ems.custom_types.query_var;
l_fy  ems.custom_types.query_var;
upload_link_id bigint;
l_num_rows integer;
l_link_number ems.custom_types.query_var;
l_link_digits character varying;
l_pattern character varying;

BEGIN

SELECT COALESCE((SELECT COUNT(*) FROM ems.ems_data_upload_link),0) INTO l_num_rows;

l_fiscalyear := substr(pkg_utility.getfiscalyear(SYSDATE), -4) ||'-';
l_pattern := 'LINK-'|| l_fiscalyear || to_char(1,'fm0000000');
IF (l_num_rows) < 1 THEN
  l_link_number := l_pattern;
ELSE
 --Return the maximum ID reached for triage number in the current fiscal year
 SELECT max(regexp_substr(upload_link_id, '[[:digit:]]+', 1, 2)) FROM ems.ems_data_upload_link WHERE
  regexp_substr(upload_link_id, '[[:digit:]]+', 1, 1) = substr(pkg_utility.getfiscalyear(SYSDATE), -4) INTO l_link_digits;

 --If it's a new fiscal year, then "l_link_digits" would be null. Restart the counter from 1 at the start of every fiscal year.
 IF (l_link_digits IS NULL) THEN
    l_link_number := l_pattern;
 ELSE
  l_link_digits := to_char((l_link_digits + 1),'fm0000000');
  l_link_number := 'LINK-'|| l_fiscalyear || l_link_digits;
 END IF;

END IF;

RETURN l_link_number;

EXCEPTION
 WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error(to_char(SQLCODE), to_char(SUBSTR(SQLERRM, 1, 3500)), to_char('pkg_utility'), to_char('generate_link_number'), -99);
    RETURN -1;
  END;
END;


END pkg_utility;

ALTER PACKAGE ems.pkg_utility OWNER TO ems_admin;
GRANT EXECUTE ON PACKAGE ems.pkg_utility TO ems_admin;
