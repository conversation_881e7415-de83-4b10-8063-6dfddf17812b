﻿-- Package: ems.pkg_audit

-- DROP PACKAGE ems.pkg_audit;

CREATE OR REPLACE PACKAGE ems.pkg_audit
AUTHID CURRENT_USER
IS

user_id bigint;

  --Overloaded PROCEDUREs: check_value are used to populate TABLE: audit_tbl

  --Data type: VARCHAR2 - One Primary Key
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new character varying, l_old character varying);

  --Data Type: DATE - One Primary Key
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new timestamp without time zone, l_old timestamp without time zone);

  --Data Type: NUMBER - One Primary Key
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new numeric, l_old numeric);

  --Data Type: JSONB - One Primary Key
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new jsonb, l_old jsonb);

  --Data Type: JSON - One Primary Key
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new json, l_old json);

  --Data Type: BYTEA - One Primary Key
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new bytea, l_old bytea);

  --Data Type: BOOLEAN - UPDATE with One Primary Key
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new boolean, l_old boolean);

  --Data Type: UUID - UPDATE with One Primary Key
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new uuid, l_old uuid);

  --Audit Domains not requested (but provided by SEI):
  PROCEDURE log_domains_not_requested(p_domain character varying, p_filename character varying);

  --Log SQL commands executed:
  PROCEDURE log_sql(p_user_session_id integer, p_user_permissions_id integer, p_start_time_date timestamp without time zone, p_end_time_date timestamp without time zone, p_sql_text character varying, p_err_sql_logging_id integer);

  --Audit DELETED primary key and NUMERIC value
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old numeric);

  --Audit DELETED primary key and VARCHAR value
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old character varying);

  --Audit DELETED primary key and TIMESTAMP WITH TIMEZONE value
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old timestamp with time zone);

  --Audit DELETED primary key and TIMESTAMP WITH TIMEZONE value
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old timestamp without time zone);

  --Data Type: INTERVAL - One Primary Key
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new interval, l_old interval);

  --Audit DELETED primary key and INTERVAL value
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old interval);

  --Audit DELETED primary key and BYTEA value
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old bytea);

  --Audit DELETED primary key with BOOLEAN value
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old boolean);

  --Audit DELETED primary key with JSON value
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old json);

  --Audit DELETED primary key with JSONB value
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old jsonb);
  PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old uuid);

END pkg_audit;

CREATE OR REPLACE PACKAGE BODY ems.pkg_audit
IS


----------------------------------------------------------------------------
-- PROCEDURE NAME:
--   check_value
--
-- AUTHOR:   Greg Walker      DATE CREATED: November 2014
--
-- DESCRIPTION:
--      This procedure is called by After Table Update Triggers named: AUD_
--      After a table is updated, the procedure captures the:
--      table name, column name, old value, new value, timestamp, and userid.
--      Procedure then populates table: audit_tbl, used for audit purposes.
--      Procedure is overloaded to handle multiple data types and PKs.
--
-- PARAMETERS:
--           Name               Type        I/O          Description
--   ---------------------- ----------  --------- -------------------------
--    l_tname                 varchar2  in          updated table name
--    l_cname                 varchar2  in          updated column name
--    l_pk1                   number    in          primary key 1
--    l_new                   varchar2  in          new updated value
--    l_old                   varchar2  in          old updated value
--
--
-- MODIFICATION HISTORY:
--   Change
--          Author              Date         ID           Description
--   ---------------------   ----------   --------   ----------------------
--   <ProgrammerName>        MM/DD/YYYY   <Tcket#>   <DescriptionOfChange>
--
-- NOTES:
--   <Additional helpful information>
-- does not currently handle data type: BLOB
----------------------------------------------------------------------------

--Data type: VARCHAR2 - One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new character varying, l_old character varying) IS

  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

begin

user_id := ems.ems_app_user.getuser();

    if ( l_new <> l_old or
         (l_new is null and l_old is not NULL) or
         (l_new is not null and l_old is NULL) )
    then
        insert into ems.ems_audit_table (
        who,
        tname,
        cname,
        pk1,
        old,
        new)
        values
        (
         ems.ems_app_user.getuser(),
         upper(l_tname),
         upper(l_cname),
         l_pk1,
         l_old,
         l_new);
    end if;

  EXCEPTION
    WHEN l_audit_err THEN
    BEGIN
      ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_valueue (Data type: VARCHAR2). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
    COMMIT;
    END;

end;

--Data Type: DATE - One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new timestamp without time zone, l_old timestamp without time zone) IS
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

begin

user_id := ems.ems_app_user.getuser();

    if ( l_new <> l_old or
         (l_new is null and l_old is not NULL) or
         (l_new is not null and l_old is NULL) )
    then
        insert into ems.ems_audit_table (
        who,
        tname,
        cname,
        pk1,
        old,
        new)
        values
        (
         ems.ems_app_user.getuser(),
         upper(l_tname),
         upper(l_cname),
         l_pk1,
         to_char( l_old, 'yyyy-mm-dd hh24:mi:ss' ),
         to_char( l_new, 'yyyy-mm-dd hh24:mi:ss' )
         );
    end if;
  EXCEPTION
    WHEN l_audit_err THEN
      BEGIN
        ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_valueue (Data Type: DATE). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
        COMMIT;
      END;
end;

--Data Type: NUMBER - One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new numeric, l_old numeric) IS
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

begin

user_id := ems.ems_app_user.getuser();

    if ( l_new <> l_old or
         (l_new is null and l_old is not NULL) or
         (l_new is not null and l_old is NULL) )
    then
        insert into ems.ems_audit_table (
        who,
        tname,
        cname,
        pk1,
        old,
        new)
        values
        (
         ems.ems_app_user.getuser(),
         upper(l_tname),
         upper(l_cname),
         l_pk1,
         to_char( l_old ),
         to_char( l_new )
         );
    end if;
  EXCEPTION
    WHEN l_audit_err THEN
      BEGIN
        ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_valueue (Data Type: NUMBER). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
        COMMIT;
      END;
END;


--Data Type: JSONB - One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new jsonb, l_old jsonb) IS

  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

begin

user_id := ems.ems_app_user.getuser();

    if ( l_new <> l_old or
         (l_new is null and l_old is not NULL) or
         (l_new is not null and l_old is NULL) )
    then
        insert into ems.ems_audit_table (
        who,
        tname,
        cname,
        pk1,
        old,
        new)
        values
        (
         ems.ems_app_user.getuser(),
         upper(l_tname),
         upper(l_cname),
         l_pk1,
         to_char( l_old ),
         to_char( l_new )
         );
    end if;
  EXCEPTION
    WHEN l_audit_err THEN
      BEGIN
        ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_valueue (Data Type: JSONB). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
        COMMIT;
      END;
END;

--Data Type: JSON - One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new json, l_old json) IS

  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

begin

user_id := ems.ems_app_user.getuser();

    if ( l_new::json::text <> l_old::json::text or
         (l_new is null and l_old is not NULL) or
         (l_new is not null and l_old is NULL) )
    then
        insert into ems.ems_audit_table (
        who,
        tname,
        cname,
        pk1,
        old,
        new)
        values
        (
         ems.ems_app_user.getuser(),
         upper(l_tname),
         upper(l_cname),
         l_pk1,
         l_old ,
         l_new
         );
    end if;
  EXCEPTION
    WHEN l_audit_err THEN
      BEGIN
        ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_valueue (Data Type: JSON). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
        COMMIT;
      END;
END;

--Data Type: BYTEA - One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new bytea, l_old bytea) IS

  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

begin

user_id := ems.ems_app_user.getuser();

    if ( l_new <> l_old or
         (l_new is null and l_old is not NULL) or
         (l_new is not null and l_old is NULL) )
    then
        insert into ems.ems_audit_table (
        who,
        tname,
        cname,
        pk1,
        old,
        new)
        values
        (
         ems.ems_app_user.getuser(),
         upper(l_tname),
         upper(l_cname),
         l_pk1,
         encode(l_old::bytea, 'hex'),
         encode(l_new::bytea, 'hex')
         );
    end if;
  EXCEPTION
    WHEN l_audit_err THEN
      BEGIN
        ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_valueue (Data Type: BYTEA). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
        COMMIT;
      END;
END;

--Data Type: BOOLEAN - UPDATE with One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new boolean, l_old boolean) IS

  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

begin

user_id := ems.ems_app_user.getuser();

    if ( l_new <> l_old or
         (l_new is null and l_old is not NULL) or
         (l_new is not null and l_old is NULL) )
    then
        insert into ems.ems_audit_table (
        who,
        tname,
        cname,
        pk1,
        old,
        new)
        values
        (
         ems.ems_app_user.getuser(),
         upper(l_tname),
         upper(l_cname),
         l_pk1,
          l_old,
          l_new
         );
    end if;
  EXCEPTION
    WHEN l_audit_err THEN
      BEGIN
        ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_valueue (Data Type: BOOLEAN). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
        COMMIT;
      END;
END;

--Data Type: UUID - UPDATE with One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new uuid, l_old uuid)  IS
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

begin

user_id := ems.ems_app_user.getuser();

    if ( l_new <> l_old or
         (l_new is null and l_old is not NULL) or
         (l_new is not null and l_old is NULL) )
    then
        insert into ems.ems_audit_table (
        who,
        tname,
        cname,
        pk1,
        old,
        new)
        values
        (
         ems.ems_app_user.getuser(),
         upper(l_tname),
         upper(l_cname),
         l_pk1,
          l_old,
          l_new
         );
    end if;
  EXCEPTION
    WHEN l_audit_err THEN
      BEGIN
        ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_valueue (Data Type: UUID). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
        COMMIT;
      END;
END;

PROCEDURE log_domains_not_requested(p_domain character varying, p_filename character varying) IS

  v_strsql ems.custom_types.query_var;
  v_user_auth_id INTEGER;
  l_domain_not_req_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

  select *
  into v_user_auth_id
  from ems.ems_app_user.getuser();

  if v_user_auth_id is not null then
    insert into ems.audit_domains_not_requested(user_auth_id, timestamp, domain, filename) values(v_user_auth_id, sysdate,p_domain,p_filename);
  end if;

  EXCEPTION
    WHEN l_domain_not_req_err THEN
      BEGIN
        ems.pkg_utility.log_sql_error('Error within ems.log_domains_not_requested. ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','log_domains_not_requested',user_id);
        COMMIT;
      END;

END;


PROCEDURE log_sql(p_user_session_id integer, p_user_permissions_id integer, p_start_time_date timestamp without time zone, p_end_time_date timestamp without time zone, p_sql_text character varying, p_err_sql_logging_id integer) IS

  v_strsql ems.custom_types.query_var;
  v_user_auth_id INTEGER;
  l_log_sql_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

  select *
  into v_user_auth_id
  from ems.ems_app_user.getuser();

    INSERT INTO ems.ems_audit_actions_sql(user_auth_id, user_session_id, user_permissions_id, start_time_date, end_time_date, sql_text, err_sql_logging_id)
      values(v_user_auth_id, p_user_session_id, p_user_permissions_id, p_start_time_date, p_end_time_date, p_sql_text, p_err_sql_logging_id);
  EXCEPTION
    WHEN l_log_sql_err THEN
      BEGIN
        ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.log_log_sql_err. ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','log_sql_err',user_id);
        COMMIT;
      END;

END;

PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old character varying) IS

  l_strsql ems.custom_types.query_var;
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

BEGIN

  user_id := ems.ems_app_user.getuser();

  INSERT INTO ems.ems_audit_table (who, tname, cname, pk1, old, operation)
  VALUES (ems.ems_app_user.getuser(), upper(l_tname), upper(l_cname), l_pk1, to_char(l_old), 'RECORD DELETED');

 EXCEPTION
  WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (DELETE operation on VARCHAR table column). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
  COMMIT;
  END;
END;

PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old numeric) IS

  l_strsql ems.custom_types.query_var;
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

INSERT INTO ems.ems_audit_table (who, tname, cname, pk1, old, operation)
VALUES (ems.ems_app_user.getuser(), upper(l_tname), upper(l_cname), l_pk1, to_char(l_old), 'RECORD DELETED');


 EXCEPTION
  WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (DELETE operation on NUMERIC table column). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
  COMMIT;
  END;
END;

PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old timestamp with time zone) IS

  l_strsql ems.custom_types.query_var;
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

INSERT INTO ems.ems_audit_table (who, tname, cname, pk1, old, operation)
VALUES (ems.ems_app_user.getuser(), upper(l_tname), upper(l_cname), l_pk1, to_char(l_old), 'RECORD DELETED');

 EXCEPTION
  WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (DELETE operation on TIMESTAMP WITH TIMEZONE table column). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
  COMMIT;
  END;
END;

PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old timestamp without time zone) IS

  l_strsql ems.custom_types.query_var;
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

INSERT INTO ems.ems_audit_table (who, tname, cname, pk1, old, operation)
VALUES (ems.ems_app_user.getuser(), upper(l_tname), upper(l_cname), l_pk1, to_char(l_old), 'RECORD DELETED');

 EXCEPTION
  WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (DELETE operation on TIMESTAMP WITHOUT TIMEZONE table column). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
  COMMIT;
  END;
END;

--Data Type: INTERVAL - One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_new interval, l_old interval) IS
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

begin

user_id := ems.ems_app_user.getuser();

    if ( l_new <> l_old or
         (l_new is null and l_old is not NULL) or
         (l_new is not null and l_old is NULL) )
    then
        insert into ems.ems_audit_table (
        who,
        tname,
        cname,
        pk1,
        old,
        new)
        values
        (
         ems.ems_app_user.getuser(),
         upper(l_tname),
         upper(l_cname),
         l_pk1,
         to_char( l_old, 'hh24:mi:ss' ),
         to_char( l_new, 'hh24:mi:ss' )
         );
    end if;

  EXCEPTION
    WHEN l_audit_err THEN
      BEGIN
        ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (Data Type: INTERVAL). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
        COMMIT;
      END;
end;

--Data Type: INTERVAL - One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old interval) IS

  l_strsql ems.custom_types.query_var;
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

INSERT INTO ems.ems_audit_table (who, tname, cname, pk1, old, operation)
VALUES (ems.ems_app_user.getuser(), upper(l_tname), upper(l_cname), l_pk1, to_char(l_old), 'RECORD DELETED');

 EXCEPTION
  WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (DELETE operation on INTERVAL table column). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
  COMMIT;
  END;
END;

--Data Type: BYTEA - One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old bytea) IS

  l_strsql ems.custom_types.query_var;
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

INSERT INTO ems.ems_audit_table (who, tname, cname, pk1, old, operation)
VALUES (ems.ems_app_user.getuser(), upper(l_tname), upper(l_cname), l_pk1, encode(l_old::bytea, 'hex'), 'RECORD DELETED');

 EXCEPTION
  WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (DELETE operation on BYTEA table column). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
  COMMIT;
  END;
END;

--Data Type: BOOLEAN - One Primary Key
PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old boolean) IS

  l_strsql ems.custom_types.query_var;
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

INSERT INTO ems.ems_audit_table (who, tname, cname, pk1, old, operation)
VALUES (ems.ems_app_user.getuser(), upper(l_tname), upper(l_cname), l_pk1, l_old, 'RECORD DELETED');

 EXCEPTION
  WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (DELETE operation on BOOLEAN table column). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
  COMMIT;
  END;
END;

PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old json) IS

  l_strsql ems.custom_types.query_var;
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

INSERT INTO ems.ems_audit_table (who, tname, cname, pk1, old, operation)
VALUES (ems.ems_app_user.getuser(), upper(l_tname), upper(l_cname), l_pk1, l_old::json::text, 'RECORD DELETED');

 EXCEPTION
  WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (DELETE operation on JSON table column). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
  COMMIT;
  END;
END;

PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old jsonb) IS

  l_strsql ems.custom_types.query_var;
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

INSERT INTO ems.ems_audit_table (who, tname, cname, pk1, old, operation)
VALUES (ems.ems_app_user.getuser(), upper(l_tname), upper(l_cname), l_pk1, l_old, 'RECORD DELETED');

 EXCEPTION
  WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (DELETE operation on JSONB table column). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
  COMMIT;
  END;
END;

PROCEDURE check_value(l_tname character varying, l_cname character varying, l_pk1 bigint, l_old uuid)  IS

  l_strsql ems.custom_types.query_var;
  l_operation ems.custom_types.query_var;
  l_audit_err EXCEPTION;

BEGIN

user_id := ems.ems_app_user.getuser();

INSERT INTO ems.ems_audit_table (who, tname, cname, pk1, old, operation) VALUES (ems.ems_app_user.getuser(), upper(l_tname), upper(l_cname),
  l_pk1, l_old, 'RECORD DELETED');

 EXCEPTION
  WHEN OTHERS THEN
  BEGIN
    ems.pkg_utility.log_sql_error('Error within ems.pkg_audit.check_value (DELETE operation on uuid table column). ' || sqlcode,  substr(sqlerrm, 1, 3500),'pkg_audit','check_value',user_id);
  COMMIT;
  END;
END;

END pkg_audit;


GRANT EXECUTE ON PACKAGE ems.pkg_audit TO public;
GRANT EXECUTE ON PACKAGE ems.pkg_audit TO ems_admin;

