#!/bin/bash

if [[ -z "$DO_COVERAGE" ]]; then
    if [ "$CI_COMMIT_BRANCH" == "development" ]; then
        DO_COVERAGE="on"
    else
        DO_COVERAGE="off"
    fi
fi

if [ -z ${EMS_DIR+x} ]; then
    EMS_DIR="../../web"
fi

if [[ -z "$RUN_LOCAL" ]]; then
    pushd $EMS_DIR/$APP_DIR
    rm shared
    ln -s ../shared shared
    popd

    enforceTimeLimit="--enforce-time-limit"
fi

ip="$(hostname --all-ip-addresses | xargs)"

# setup bootstrap file...
echo "<?php" > bootstrap.php
echo "require_once('phpunit/EmsTestCase.php');" >> bootstrap.php
echo "require_once('phpunit/autoloader.php');" >> bootstrap.php
echo "require_once('phpunit/constants.php');" >> bootstrap.php
echo "require_once('$EMS_DIR/shared/php/constants.php');" >> bootstrap.php
echo "require_once('$EMS_DIR/$APP_DIR/src/autoloader.php');" >> bootstrap.php
echo "require_once('$EMS_DIR/shared/php/autoloader.php');" >> bootstrap.php
echo "require_once('$EMS_DIR/shared/vendor/php/autoloader.php');" >> bootstrap.php
echo "require_once('$EMS_DIR/shared/php/settings_file.php');" >> bootstrap.php
echo "\$settings = get_settings('$EMS_DIR/$APP_DIR/config', '$EMS_DIR/shared/config');" >> bootstrap.php
echo "define('SETTINGS', \$settings);" >> bootstrap.php
echo "define('APP_DIR', '$APP_DIR');" >> bootstrap.php
echo "define('EMS_URL', '$EMS_URL');" >> bootstrap.php
echo "define('SSLKEYPASSWD', 'P@ssw0rd123!@#');" >> bootstrap.php
echo "define('TEST_SERVER_IP', '$ip');" >> bootstrap.php

# constants for services that exist in our test data
echo "define('DC3_DCISE_SERVICE_ID', '1');" >> bootstrap.php
echo "define('DC3_DCISE_SERVICE', 'dc3-dcise');" >> bootstrap.php
echo "define('DC3_DCISE_SERVICE_TOKEN', 'p3pVlU4TId5Ruxm8-YJYMgJq4s7RCR4J');" >> bootstrap.php
echo "define('DC3_AG_SERVICE_ID', '2');" >> bootstrap.php
echo "define('DC3_AG_SERVICE', 'dc3-ag');" >> bootstrap.php
echo "define('DC3_AG_SERVICE_TOKEN', 'sHOBaeJ_OOgQ74Nt5-PXzGI-OkYLtnpv');" >> bootstrap.php

# common certs that map to a user id in the test db - ONLY USE THESE IN EXTERNAL UNIT TESTS
echo "define('APPLICATION_ADMINISTRATOR', 96886927495);" >> bootstrap.php
echo "define('EXTERNAL_ADVANCED', 82377201335);" >> bootstrap.php
echo "define('EXTERNAL_BASIC', 97823862824);" >> bootstrap.php
echo "define('GENERIC_USER_2', 86766798511);" >> bootstrap.php
echo "define('GENERIC_USER_7', 95097426799);" >> bootstrap.php
echo "define('GENERIC_USER_8', 49218489969);" >> bootstrap.php
echo "define('GENERIC_USER_9', 39569011578);" >> bootstrap.php
echo "define('GOLDRUSH_TEST', 59857504807);" >> bootstrap.php
echo "define('INTERNAL_ADVANCED', 80226619748);" >> bootstrap.php
echo "define('INTERNAL_BASIC', 88884438051);" >> bootstrap.php
echo "define('GENERIC_USER_4', 69432618133);" >> bootstrap.php
echo "define('COLUMBO_TEST', 32555729610);" >> bootstrap.php

# unregistered user - ONLY USE THIS IN EXTERNAL UNIT TESTS
echo "define('EMS_TEST', 'EMS_TEST');" >> bootstrap.php

# common certs that map to a user id in the test db - ONLY USE THESE IN INTERNAL UNIT TESTS
echo "define('APPLICATION_MANAGER', 42799355289);" >> bootstrap.php
echo "define('COMMON_TEST', 91946007489);" >> bootstrap.php
echo "define('GENERIC_USER_3', 39277334930);" >> bootstrap.php
echo "define('GROK_TEST', 20941018884);" >> bootstrap.php
echo "define('INTERNAL_DATA_ANALYST', 70630300697);" >> bootstrap.php
echo "define('INTERNAL_MANAGER', 37146765308);" >> bootstrap.php

# used for userpass login external only
echo "define('SYSTEM_ADMINISTRATOR', 45941363791);" >> bootstrap.php

# unregistered user - ONLY USE THIS IN INTERNAL UNIT TESTS
echo "define('NEW_USER', 'NEW_USER');" >> bootstrap.php

# unused and registered
echo "define('DAD_TEST', 87549765966);" >> bootstrap.php
echo "define('GENERIC_USER_0', 27580767842);" >> bootstrap.php
echo "define('GENERIC_USER_1', 95301179034);" >> bootstrap.php
echo "define('GENERIC_USER_5', 1082675044);" >> bootstrap.php
echo "define('GENERIC_USER_6', 1416821226);" >> bootstrap.php
echo "define('LEADERSHIP_ADVANCED', 1805082771);" >> bootstrap.php

# unused and unregistered
echo "define('GENERIC_USER_10', 'GENERIC_USER_10');" >> bootstrap.php
echo "define('MAINTENANCE_MODE', 'MAINTENANCE_MODE');" >> bootstrap.php

# common certs that do not map to a user id in the test db
echo "define('EXPIRED_ENTITY', 'EXPIRED_ENTITY');" >> bootstrap.php
echo "define('REVOKED_ENTITY', 'REVOKED_ENTITY');" >> bootstrap.php
echo "define('UNTRUSTED_ENTITY', 'UNTRUSTED_ENTITY');" >> bootstrap.php

if [ "$APP_DIR" == 'internal-app' ]; then
    echo "define('EMS_ADMIN_ACTIVE', APPLICATION_MANAGER);" >> bootstrap.php
    echo "define('NON_DC3_AG_CUSTOMER_ACTIVE', GROK_TEST);" >> bootstrap.php
    echo "define('NON_DC3_DCISE_CUSTOMER_ACTIVE', COMMON_TEST);" >> bootstrap.php
    echo "define('NON_DC3_OTHER_CUSTOMER_PENDING', GENERIC_USER_3);" >> bootstrap.php
    echo "define('DCISE_MAC_ACTIVE', INTERNAL_DATA_ANALYST);" >> bootstrap.php
    echo "define('AG_MAC_ACTIVE', INTERNAL_MANAGER);" >> bootstrap.php
    echo "define('UNREGISTERED_USER', NEW_USER);" >> bootstrap.php
else
    echo "define('EMS_ADMIN_ACTIVE', APPLICATION_ADMINISTRATOR);" >> bootstrap.php
    echo "define('NON_DC3_AG_CUSTOMER_ACTIVE', EXTERNAL_BASIC);" >> bootstrap.php
    echo "define('NON_DC3_DCISE_CUSTOMER_ACTIVE', EXTERNAL_ADVANCED);" >> bootstrap.php
    echo "define('DCISE_MAC_ACTIVE', INTERNAL_BASIC);" >> bootstrap.php
    echo "define('AG_MAC_ACTIVE', INTERNAL_ADVANCED);" >> bootstrap.php
    echo "define('DC3_CFL_CUSTOMER_ACTIVE', COLUMBO_TEST);" >> bootstrap.php
    echo "define('DC3_CFL_CUSTOMER_PENDING_REACTIVATION', GENERIC_USER_2);" >> bootstrap.php
    echo "define('DC3_CFL_CUSTOMER_PENDING', GENERIC_USER_7);" >> bootstrap.php
    echo "define('DC3_AG_CUSTOMER_ACTIVE', GENERIC_USER_8);" >> bootstrap.php
    echo "define('DC3_DCISE_CUSTOMER_ACTIVE', GENERIC_USER_4);" >> bootstrap.php
    echo "define('DC3_CFL_CUSTOMER_DENIED', GENERIC_USER_9);" >> bootstrap.php
    echo "define('DIB_PARTNER_DCISE_CUSTOMER_ACTIVE', GOLDRUSH_TEST);" >> bootstrap.php
    echo "define('UNREGISTERED_USER', EMS_TEST);" >> bootstrap.php
fi

if [[ -z "$RUN_LOCAL" ]]; then
    echo "define('DB_SETTINGS', ['host' => '$DB_HOST', 'port' => '$DB_PORT', 'dbname' => '$DB_NAME', 'user' => '$DB_USER']);" >> bootstrap.php
    echo "putenv(\"PGSSLCERT=$DB_CERT_FILE\");" >> bootstrap.php
    echo "putenv(\"PGSSLKEY=$DB_KEY_FILE\");" >> bootstrap.php
    echo "putenv(\"PGSSLROOTCERT=$DB_CA_BUNDLE\");" >> bootstrap.php
    echo "putenv(\"PGSSLMODE=$DB_SSL_MODE\");" >> bootstrap.php
else
    echo "define('DB_SETTINGS', ['host' => '$DB_HOST', 'port' => '$DB_PORT', 'dbname' => '$DB_NAME', 'user' => '$DB_USER', 'password' => '$DB_USER_PWD']);" >> bootstrap.php
fi
# echo "echo(print_r(DB_SETTINGS, true));" >> bootstrap.php
echo "?>" >> bootstrap.php

if hash php 2>/dev/null; then
    PHP="php"
else
    echo "Can't find php, dying";
    exit -1
fi

WEB_DIR="/srv/www/$EMS_DEPLOY_DIR/$APP_DIR"

# setup stuff for API code coverage... this is a pain
if [ "$DO_COVERAGE" == "on" ]; then
    COVERAGE_PORT=`shuf -i 2000-65000 -n 1`

    INDEX_PHP="$WEB_DIR/api/index.php"
    mv $INDEX_PHP ${INDEX_PHP}.real

    echo "<?php" > pwd-setup.php
    echo "\$coverage_service = \"localhost:$COVERAGE_PORT\";" >> pwd-setup.php
    echo "\$SRC_DIR = \"$WEB_DIR/src\";" >> pwd-setup.php
    echo "\$SHARED_DIR = \"$WEB_DIR/shared/php\";" >> pwd-setup.php
    echo "?>" >> pwd-setup.php

    cp phpunit*.phar $WEB_DIR/api
    chmod 644 $WEB_DIR/api/phpunit*.phar
    PHPUNIT="`ls $WEB_DIR/api/phpunit-*.phar`"
    export PHPUNIT

    cat pwd-setup.php code-coverage-cheat.php > $INDEX_PHP
    chmod 644 $INDEX_PHP
    $PHP -S "localhost:$COVERAGE_PORT" collect-coverage.php & # this service collects the coverages sent back
    COVERAGE_PID=$!
fi

# actually run tests

if [ -z ${PHPUNIT+x} ]; then
    PHPUNIT="phpunit-8.1.3.phar"
fi

if [ "$DO_COVERAGE" == "on" ]; then
    if [ ! -d "test-output" ]; then
        echo "Creating test-output/ directory..."
        mkdir "test-output"
    fi
fi

if [ "$STOP_ON_FAILURE" == "on" ]; then
    stopOnFailure="--stop-on-failure"
fi

PHPUNIT_OPTIONS="$enforceTimeLimit $stopOnFailure --verbose --colors=always --bootstrap ./bootstrap.php --testsuite $APP_DIR $GROUP"

INCLUDE_PATH=".:$WEB_DIR/."

echo "\$PHPUNIT: $PHPUNIT"
echo "\$PHPUNIT_OPTIONS: $PHPUNIT_OPTIONS"
echo "\$DO_COVERAGE: $DO_COVERAGE"

$PHP -d include_path=$INCLUDE_PATH $PHPUNIT $PHPUNIT_OPTIONS
((FAILED += $?))

if [ "$DO_COVERAGE" == "on" ]; then
    # cleanup code coverage
    rm $INDEX_PHP
    mv $INDEX_PHP.real $INDEX_PHP
    chmod 644 $INDEX_PHP
    kill $COVERAGE_PID
    mkdir -p /srv/www/$EMS_DEPLOY_DIR/test-output
    mkdir -p /srv/www/$EMS_DEPLOY_DIR/test-output/$APP_DIR
    cp -rf "test-output" /srv/www/$EMS_DEPLOY_DIR
fi

exit $FAILED
